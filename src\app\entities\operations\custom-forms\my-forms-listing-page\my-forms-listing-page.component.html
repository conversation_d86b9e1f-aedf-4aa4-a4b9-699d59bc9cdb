<nb-card class="customerSpinner appSpinner" [nbSpinner]="loading" nbSpinnerStatus="primary" nbSpinnerSize="large">
  <nb-card-header>
    <div class="row">
      <div class="col-12 d-flex align-items-center"></div>
    </div>
    <div class="row">
      <div class="col-12 d-flex justify-content-between align-items-center">
        <h6>Forms</h6>
        <div>
          <button
            class="linear-mode-button me-2"
            nbButton
            status="primary"
            size="small"
            [disabled]="loading"
            (click)="gotoTemplates()"
            type="button"
            *ngIf="userRole === 'admin' || userRole === 'commercialAssetsManager' || userRole === 'fieldTech'"
          >
            <span class="d-none d-md-inline-block">Go to Templates</span>
            <em class="d-inline-block d-md-none fa-regular fa-square-plus"></em>
          </button>
          <button
            class="linear-mode-button me-2"
            nbButton
            status="primary"
            size="small"
            [disabled]="loading"
            (click)="openModal(addForm)"
            type="button"
            *ngIf="userRole === 'admin' || userRole === 'commercialAssetsManager' || userRole === 'fieldTech'"
          >
            <span class="d-none d-md-inline-block">Add Form</span>
            <em class="d-inline-block d-md-none fa-regular fa-square-plus"></em>
          </button>
          <button
            class="linear-mode-button"
            nbButton
            status="basic"
            size="small"
            [disabled]="loading"
            (click)="goToLandingPage()"
            type="button"
          >
            <span class="d-none d-lg-inline-block">Back</span>
            <i class="d-inline-block d-lg-none fa-solid fa-arrow-left"></i>
          </button>
        </div>
      </div>
    </div>
  </nb-card-header>
  <nb-card-body>
    <div class="row">
      <div class="col-12 customerFilter appFilter">
        <sfl-filter
          [filterDetails]="filterDetails"
          (refreshList)="refreshList($event)"
          (clearParentList)="formsListingData = []"
          (refreshTableHeight)="this.isFilterDisplay = $event"
        ></sfl-filter>
      </div>
      <div id="fixed-table" setTableHeight [isFilterDisplay]="isFilterDisplay" class="col-12 table-responsive mt-3 table-card-view">
        <div *ngIf="currrentTemplateType">
          <ng-container [ngSwitch]="currrentTemplateType">
            <ng-container *ngSwitchCase="qestFormTemplateTypes.QEST_INVERTER_PM">
              <ng-template [ngTemplateOutlet]="inverterPMFormTable"></ng-template>
            </ng-container>
            <ng-container *ngSwitchCase="qestFormTemplateTypes.QEST_COVER_PAGE">
              <ng-template [ngTemplateOutlet]="coverPageFormTable"></ng-template>
            </ng-container>
            <ng-container *ngSwitchCase="qestFormTemplateTypes.QEST_SUMMARY_REPORT">
              <ng-template [ngTemplateOutlet]="summaryReportFormTable"></ng-template>
            </ng-container>
            <ng-container *ngSwitchCase="qestFormTemplateTypes.QEST_MODULE_TORQUE">
              <ng-template [ngTemplateOutlet]="moduleTorqueFormTable"></ng-template>
            </ng-container>
            <ng-container *ngSwitchCase="qestFormTemplateTypes.QEST_TPM_FORM">
              <ng-template [ngTemplateOutlet]="tpmFormTable"></ng-template>
            </ng-container>
          </ng-container>
        </div>
        <div class="mt-4 d-flex align-items-center justify-content-center" *ngIf="!currrentTemplateType">
          <span class="no-record text-center">No Data Found</span>
        </div>
      </div>
      <div class="mt-2 d-md-flex align-items-center" *ngIf="formsListingData?.length && currrentTemplateType">
        <div class="d-flex align-items-center">
          <label class="mb-0">Items per page: </label>
          <ng-select
            class="ms-2"
            id="formListing"
            [(ngModel)]="pageSize"
            [clearable]="false"
            [searchable]="false"
            (change)="onChangeSize()"
            appendTo="body"
          >
            <ng-option value="5">5</ng-option>
            <ng-option value="10">10</ng-option>
            <ng-option value="50">50</ng-option>
            <ng-option value="100">100</ng-option>
          </ng-select>
        </div>
        <strong class="ms-md-3">Total: {{ totalCount }}</strong>
        <div class="ms-md-auto ms-sm-0">
          <pagination-controls id="formListing" (pageChange)="onPageChange($event)" class="paginate"></pagination-controls>
        </div>
      </div>
    </div>
    <div *ngIf="!formsListingData?.length">
      <ng-container *ngTemplateOutlet="noTemplateYet"></ng-container>
    </div>
  </nb-card-body>
</nb-card>

<ng-template #addForm>
  <div class="alert-box">
    <div class="modal-header">
      <h6 class="modal-title">Add Form</h6>
      <button type="button" class="close" aria-label="Close" (click)="onCancel()">
        <span aria-hidden="true"><em class="fa-solid fa-xmark fa-xl"></em></span>
      </button>
    </div>
    <div class="modal-body">
      <form #formDetailsForm="ngForm">
        <div class="row">
          <div class="col-12 mb-2">
            <label class="label" for="form-name"> Form Name <span class="ms-1 text-danger">*</span></label>
            <div>
              <input
                id="form-name"
                name="formName"
                class="form-control"
                [(ngModel)]="customFormService.formName"
                #formName="ngModel"
                placeholder="Enter Form Name"
                nbInput
                fullWidth
                required
              />
              <sfl-error-msg [control]="formName" fieldName="Form Name"></sfl-error-msg>
            </div>
          </div>
          <div class="col-12 mb-2">
            <label class="label" for="template">Template<span class="ms-1 text-danger">*</span></label>
            <div>
              <ng-select
                id="template"
                name="template"
                bindLabel="templateName"
                bindValue="templateId"
                [items]="templateDropdownData"
                notFoundText="No template Found"
                placeholder="Select template"
                [clearable]="false"
                [(ngModel)]="customFormService.selectedTemplateId"
                (change)="getSelectedTemplateDetails($event)"
                #selectedTemplateId="ngModel"
                required
              >
              </ng-select>
              <sfl-error-msg [control]="selectedTemplateId" fieldName="Template"></sfl-error-msg>
            </div>
          </div>
          <div class="col-12 mb-2" *ngIf="isCoverPageTemplate">
            <label class="label" for="templateType">Template Type<span class="ms-1 text-danger">*</span></label>
            <div>
              <ng-select
                name="templateType"
                bindLabel="templateTypeName"
                bindValue="templateTypeId"
                [items]="templateTypesList"
                notFoundText="No template Type Found"
                placeholder="Select template Type"
                [clearable]="false"
                [(ngModel)]="customFormService.coverType"
                #templateType="ngModel"
                required
              >
              </ng-select>
              <sfl-error-msg [control]="templateType" fieldName="Template Type"></sfl-error-msg>
            </div>
          </div>
          <div class="col-12">
            <label class="label" for="note">Note</label>
            <textarea
              id="note"
              name="note"
              placeholder="Enter Note"
              class="form-control"
              [(ngModel)]="customFormService.formNote"
              nbInput
              fullWidth
            ></textarea>
          </div>
        </div>
      </form>
    </div>
    <div class="modal-footer">
      <div class="mb-4 d-flex justify-content-end align-items-center">
        <button
          class="linear-mode-button me-3"
          nbButton
          status="secondary"
          size="small"
          [disabled]="loading"
          (click)="onCancel()"
          type="button"
        >
          Cancel
        </button>
        <button
          class="linear-mode-button me-3"
          nbButton
          status="primary"
          size="small"
          [disabled]="loading"
          (click)="createForm(formDetailsForm)"
          type="submit"
        >
          Add
        </button>
      </div>
    </div>
  </div>
</ng-template>

<ng-template #inverterPMFormTable>
  <table class="table table-hover table-bordered" aria-describedby="Customer List">
    <thead>
      <tr>
        <th (click)="sort('templateTypeName', sortOptionList['templateTypeName'])" id="templateTypeName">
          <div class="d-flex align-items-center">
            Template Type
            <span
              class="fa text-center cursor-pointer ms-auto"
              [ngClass]="{
                'fa-arrow-up': sortOptionList['templateTypeName'] === 'desc',
                'fa-arrow-down': sortOptionList['templateTypeName'] === 'asc',
                'icon-selected': filterModel.sortBy === 'templateTypeName'
              }"
            ></span>
          </div>
        </th>
        <th (click)="sort('formName', sortOptionList['formName'])" id="formName">
          <div class="d-flex align-items-center">
            Form Name
            <span
              class="fa text-center cursor-pointer ms-auto"
              [ngClass]="{
                'fa-arrow-up': sortOptionList['formName'] === 'desc',
                'fa-arrow-down': sortOptionList['formName'] === 'asc',
                'icon-selected': filterModel.sortBy === 'formName'
              }"
            ></span>
          </div>
        </th>
        <th class="text-center" id="Customer">Customer</th>
        <th class="text-center" id="Equipment">Equipment</th>
        <th class="text-center" id="Template Name">Template Name</th>
        <th class="text-center" id="Status">Status</th>
        <th (click)="sort('updatedDate', sortOptionList['updatedDate'])" id="updatedDate">
          <div class="d-flex align-items-center">
            Last Updated On
            <span
              class="fa text-center cursor-pointer ms-auto"
              [ngClass]="{
                'fa-arrow-up': sortOptionList['updatedDate'] === 'desc',
                'fa-arrow-down': sortOptionList['updatedDate'] === 'asc',
                'icon-selected': filterModel.sortBy === 'updatedDate'
              }"
            ></span>
          </div>
        </th>
        <th (click)="sort('createdBy', sortOptionList['createdBy'])" id="createdBy">
          <div class="d-flex align-items-center">
            Created By
            <span
              class="fa text-center cursor-pointer ms-auto"
              [ngClass]="{
                'fa-arrow-up': sortOptionList['createdBy'] === 'desc',
                'fa-arrow-down': sortOptionList['createdBy'] === 'asc',
                'icon-selected': filterModel.sortBy === 'createdBy'
              }"
            ></span>
          </div>
        </th>
        <th class="text-center min-width-action" id="action">Action</th>
      </tr>
    </thead>
    <tbody>
      <tr
        *ngFor="
          let formList of formsListingData
            | paginate
              : {
                  itemsPerPage: filterModel.itemsCount,
                  currentPage: currentPage,
                  totalItems: totalCount,
                  id: 'formListing'
                }
        "
      >
        <td data-title="Template Type">{{ formList.templateTypeName }}</td>
        <td data-title="Form Name">{{ formList.formName }}</td>
        <td data-title="Customer">{{ formList.customerName }}</td>
        <td data-title="Equipment">
          <span *ngIf="formList.equipmentName" nbTooltip="{{ formList?.equipmentName }}" nbTooltipPlacement="top" nbTooltipStatus="primary">
            <sfl-read-more [content]="formList?.equipmentName"></sfl-read-more>
          </span>
        </td>
        <td data-title="Template Name">{{ formList.templateName }}</td>
        <td data-title="Status" class="text-center">
          <nb-toggle
            status="primary"
            [(checked)]="formList.isActive"
            (checkedChange)="changeStatus(formList?.qestFormId, $event)"
            [disabled]="!(formList.customerName && formList.equipmentName)"
            *ngIf="userRole === 'admin' || userRole === 'commercialAssetsManager' || userRole === 'fieldTech'; else notadminandcam"
          ></nb-toggle>
          <ng-template #notadminandcam>
            {{ formList.isActive ? 'Active' : 'Inactive' }}
          </ng-template>
        </td>
        <td data-title="Last Updated On">
          {{ (formList.updatedDate | date : dateFormat) || '-' }}
        </td>
        <td data-title="Created By">{{ formList.createdBy }}</td>
        <td data-title="Action" class="text-center customer-action">
          <div>
            <em
              class="fa fa-copy text-primary cursor-pointer me-2"
              nbTooltip="Clone Form"
              nbTooltipPlacement="top"
              nbTooltipStatus="primary"
              (click)="cloneQESTForm(formList.qestFormId)"
              *ngIf="userRole === 'admin' || userRole === 'commercialAssetsManager' || userRole === 'fieldTech'"
            ></em>
            <em
              class="fa fa-eye cursor-pointer text-primary me-2"
              nbTooltip="View"
              nbTooltipPlacement="top"
              nbTooltipStatus="primary"
              (click)="customFormService.previewFormOrTemplate(formList.qestFormId, true)"
              aria-hidden="true"
              *ngIf="userRole === 'admin' || userRole === 'commercialAssetsManager' || userRole === 'fieldTech'"
            ></em>
            <em
              class="fa fa-edit cursor-pointer text-primary me-2"
              nbTooltip="Edit"
              nbTooltipPlacement="top"
              nbTooltipStatus="primary"
              aria-hidden="true"
              (click)="goToEditFormPage(formList.qestFormId)"
              *ngIf="userRole === 'admin' || userRole === 'commercialAssetsManager' || userRole === 'fieldTech'"
            ></em>
            <em
              class="fa fa-trash cursor-pointer text-danger"
              nbTooltip="Delete"
              nbTooltipPlacement="top"
              nbTooltipStatus="primary"
              aria-hidden="true"
              (click)="onFormDelete(formList.qestFormId)"
              *ngIf="userRole === 'admin' || userRole === 'commercialAssetsManager' || userRole === 'fieldTech'"
            ></em>
          </div>
        </td>
      </tr>
      <tr>
        <td colspan="9" *ngIf="!formsListingData?.length" class="no-record text-center">No Data Found</td>
      </tr>
    </tbody>
  </table>
</ng-template>

<ng-template #coverPageFormTable>
  <table class="table table-hover table-bordered" aria-describedby="Customer List">
    <thead>
      <tr>
        <th (click)="sort('templateTypeName', sortOptionList['templateTypeName'])" id="templateTypeName">
          <div class="d-flex align-items-center">
            Template Type
            <span
              class="fa text-center cursor-pointer ms-auto"
              [ngClass]="{
                'fa-arrow-up': sortOptionList['templateTypeName'] === 'desc',
                'fa-arrow-down': sortOptionList['templateTypeName'] === 'asc',
                'icon-selected': filterModel.sortBy === 'templateTypeName'
              }"
            ></span>
          </div>
        </th>
        <th (click)="sort('formName', sortOptionList['formName'])" id="formName">
          <div class="d-flex align-items-center">
            Form Name
            <span
              class="fa text-center cursor-pointer ms-auto"
              [ngClass]="{
                'fa-arrow-up': sortOptionList['formName'] === 'desc',
                'fa-arrow-down': sortOptionList['formName'] === 'asc',
                'icon-selected': filterModel.sortBy === 'formName'
              }"
            ></span>
          </div>
        </th>
        <th class="text-center" id="Template Name">Template Name</th>
        <th class="text-center" id="Status">Status</th>
        <th (click)="sort('updatedDate', sortOptionList['updatedDate'])" id="updatedDate">
          <div class="d-flex align-items-center">
            Last Updated On
            <span
              class="fa text-center cursor-pointer ms-auto"
              [ngClass]="{
                'fa-arrow-up': sortOptionList['updatedDate'] === 'desc',
                'fa-arrow-down': sortOptionList['updatedDate'] === 'asc',
                'icon-selected': filterModel.sortBy === 'updatedDate'
              }"
            ></span>
          </div>
        </th>
        <th (click)="sort('createdBy', sortOptionList['createdBy'])" id="createdBy">
          <div class="d-flex align-items-center">
            Created By
            <span
              class="fa text-center cursor-pointer ms-auto"
              [ngClass]="{
                'fa-arrow-up': sortOptionList['createdBy'] === 'desc',
                'fa-arrow-down': sortOptionList['createdBy'] === 'asc',
                'icon-selected': filterModel.sortBy === 'createdBy'
              }"
            ></span>
          </div>
        </th>
        <th class="text-center min-width-action" id="action">Action</th>
      </tr>
    </thead>
    <tbody>
      <tr
        *ngFor="
          let formList of formsListingData
            | paginate
              : {
                  itemsPerPage: filterModel.itemsCount,
                  currentPage: currentPage,
                  totalItems: totalCount,
                  id: 'formListing'
                }
        "
      >
        <td data-title="Template Type">{{ formList.templateTypeName }}</td>
        <td data-title="Form Name">{{ formList.formName }}</td>
        <td data-title="Template Name">{{ formList.templateName }}</td>
        <td data-title="Status" class="text-center">
          <nb-toggle
            status="primary"
            [(checked)]="formList.isActive"
            (checkedChange)="changeStatus(formList?.qestFormId, $event)"
            *ngIf="userRole === 'admin' || userRole === 'commercialAssetsManager' || userRole === 'fieldTech'; else notadminandcam"
          ></nb-toggle>
          <ng-template #notadminandcam>
            {{ formList.isActive ? 'Active' : 'Inactive' }}
          </ng-template>
        </td>
        <td data-title="Last Updated On">
          {{ (formList.updatedDate | date : dateFormat) || '-' }}
        </td>
        <td data-title="Created By">{{ formList.createdBy }}</td>
        <td data-title="Action" class="text-center customer-action">
          <div>
            <em
              class="fa fa-copy text-primary cursor-pointer me-2"
              nbTooltip="Clone Form"
              nbTooltipPlacement="top"
              nbTooltipStatus="primary"
              (click)="cloneQESTForm(formList.qestFormId)"
              *ngIf="userRole === 'admin' || userRole === 'commercialAssetsManager' || userRole === 'fieldTech'"
            ></em>
            <em
              class="fa fa-eye cursor-pointer text-primary me-2"
              nbTooltip="View"
              nbTooltipPlacement="top"
              nbTooltipStatus="primary"
              (click)="customFormService.previewFormOrTemplate(formList.qestFormId, true)"
              aria-hidden="true"
              *ngIf="userRole === 'admin' || userRole === 'commercialAssetsManager' || userRole === 'fieldTech'"
            ></em>
            <em
              class="fa fa-edit cursor-pointer text-primary me-2"
              nbTooltip="Edit"
              nbTooltipPlacement="top"
              nbTooltipStatus="primary"
              aria-hidden="true"
              (click)="goToEditFormPage(formList.qestFormId)"
              *ngIf="userRole === 'admin' || userRole === 'commercialAssetsManager' || userRole === 'fieldTech'"
            ></em>
            <em
              class="fa fa-trash cursor-pointer text-danger"
              nbTooltip="Delete"
              nbTooltipPlacement="top"
              nbTooltipStatus="primary"
              aria-hidden="true"
              (click)="onFormDelete(formList.qestFormId)"
              *ngIf="userRole === 'admin' || userRole === 'commercialAssetsManager' || userRole === 'fieldTech'"
            ></em>
          </div>
        </td>
      </tr>
      <tr>
        <td colspan="9" *ngIf="!formsListingData?.length" class="no-record text-center">No Data Found</td>
      </tr>
    </tbody>
  </table>
</ng-template>

<ng-template #summaryReportFormTable>
  <table class="table table-hover table-bordered" aria-describedby="Summary Reeport Form List">
    <thead>
      <tr>
        <th (click)="sort('templateTypeName', sortOptionList['templateTypeName'])" id="templateTypeName">
          <div class="d-flex align-items-center">
            Form Type
            <span
              class="fa text-center cursor-pointer ms-auto"
              [ngClass]="{
                'fa-arrow-up': sortOptionList['templateTypeName'] === 'desc',
                'fa-arrow-down': sortOptionList['templateTypeName'] === 'asc',
                'icon-selected': filterModel.sortBy === 'templateTypeName'
              }"
            ></span>
          </div>
        </th>
        <th (click)="sort('formName', sortOptionList['formName'])" id="formName">
          <div class="d-flex align-items-center">
            Form Name
            <span
              class="fa text-center cursor-pointer ms-auto"
              [ngClass]="{
                'fa-arrow-up': sortOptionList['formName'] === 'desc',
                'fa-arrow-down': sortOptionList['formName'] === 'asc',
                'icon-selected': filterModel.sortBy === 'formName'
              }"
            ></span>
          </div>
        </th>
        <th class="text-center" id="Template Name">Template Name</th>
        <th class="text-center" id="Status">Status</th>
        <th (click)="sort('updatedDate', sortOptionList['updatedDate'])" id="updatedDate">
          <div class="d-flex align-items-center">
            Last Updated On
            <span
              class="fa text-center cursor-pointer ms-auto"
              [ngClass]="{
                'fa-arrow-up': sortOptionList['updatedDate'] === 'desc',
                'fa-arrow-down': sortOptionList['updatedDate'] === 'asc',
                'icon-selected': filterModel.sortBy === 'updatedDate'
              }"
            ></span>
          </div>
        </th>
        <th class="text-center min-width-action" id="action">Action</th>
      </tr>
    </thead>
    <tbody>
      <tr
        *ngFor="
          let formList of formsListingData
            | paginate
              : {
                  itemsPerPage: filterModel.itemsCount,
                  currentPage: currentPage,
                  totalItems: totalCount,
                  id: 'formListing'
                }
        "
      >
        <td data-title="Template Type">{{ formList.templateTypeName }}</td>
        <td data-title="Form Name">{{ formList.formName }}</td>
        <td data-title="Template Name">{{ formList.templateName }}</td>
        <td data-title="Status" class="text-center">
          <nb-toggle
            status="primary"
            [(checked)]="formList.isActive"
            (checkedChange)="changeStatus(formList?.qestFormId, $event)"
            *ngIf="userRole === 'admin' || userRole === 'commercialAssetsManager' || userRole === 'fieldTech'; else notadminandcam"
          ></nb-toggle>
          <ng-template #notadminandcam>
            {{ formList.isActive ? 'Active' : 'Inactive' }}
          </ng-template>
        </td>
        <td data-title="Last Updated On">
          {{ (formList.updatedDate | date : dateFormat) || '-' }}
        </td>
        <td data-title="Action" class="text-center customer-action">
          <div>
            <em
              class="fa fa-copy text-primary cursor-pointer me-2"
              nbTooltip="Clone Form"
              nbTooltipPlacement="top"
              nbTooltipStatus="primary"
              (click)="cloneQESTForm(formList.qestFormId)"
              *ngIf="userRole === 'admin' || userRole === 'commercialAssetsManager' || userRole === 'fieldTech'"
            ></em>
            <em
              class="fa fa-eye cursor-pointer text-primary me-2"
              nbTooltip="View"
              nbTooltipPlacement="top"
              nbTooltipStatus="primary"
              (click)="customFormService.previewFormOrTemplate(formList.qestFormId, true)"
              aria-hidden="true"
              *ngIf="userRole === 'admin' || userRole === 'commercialAssetsManager' || userRole === 'fieldTech'"
            ></em>
            <em
              class="fa fa-edit cursor-pointer text-primary me-2"
              nbTooltip="Edit"
              nbTooltipPlacement="top"
              nbTooltipStatus="primary"
              aria-hidden="true"
              (click)="goToEditFormPage(formList.qestFormId)"
              *ngIf="userRole === 'admin' || userRole === 'commercialAssetsManager' || userRole === 'fieldTech'"
            ></em>
            <em
              class="fa fa-trash cursor-pointer text-danger"
              nbTooltip="Delete"
              nbTooltipPlacement="top"
              nbTooltipStatus="primary"
              aria-hidden="true"
              (click)="onFormDelete(formList.qestFormId)"
              *ngIf="userRole === 'admin' || userRole === 'commercialAssetsManager' || userRole === 'fieldTech'"
            ></em>
          </div>
        </td>
      </tr>
      <tr>
        <td colspan="9" *ngIf="!formsListingData?.length" class="no-record text-center">No Data Found</td>
      </tr>
    </tbody>
  </table>
</ng-template>

<ng-template #moduleTorqueFormTable>
  <table class="table table-hover table-bordered" aria-describedby="Customer List">
    <thead>
      <tr>
        <th (click)="sort('templateTypeName', sortOptionList['templateTypeName'])" id="templateTypeName">
          <div class="d-flex align-items-center">
            Form Type
            <span
              class="fa text-center cursor-pointer ms-auto"
              [ngClass]="{
                'fa-arrow-up': sortOptionList['templateTypeName'] === 'desc',
                'fa-arrow-down': sortOptionList['templateTypeName'] === 'asc',
                'icon-selected': filterModel.sortBy === 'templateTypeName'
              }"
            ></span>
          </div>
        </th>
        <th (click)="sort('formName', sortOptionList['formName'])" id="formName">
          <div class="d-flex align-items-center">
            Form Name
            <span
              class="fa text-center cursor-pointer ms-auto"
              [ngClass]="{
                'fa-arrow-up': sortOptionList['formName'] === 'desc',
                'fa-arrow-down': sortOptionList['formName'] === 'asc',
                'icon-selected': filterModel.sortBy === 'formName'
              }"
            ></span>
          </div>
        </th>
        <th class="text-center" id="Customer">Customer</th>
        <th class="text-center" id="Version">Version #</th>
        <th class="text-center" id="Template Name">Template Name</th>
        <th class="text-center" id="Status">Status</th>
        <th (click)="sort('updatedDate', sortOptionList['updatedDate'])" id="updatedDate">
          <div class="d-flex align-items-center">
            Last Updated On
            <span
              class="fa text-center cursor-pointer ms-auto"
              [ngClass]="{
                'fa-arrow-up': sortOptionList['updatedDate'] === 'desc',
                'fa-arrow-down': sortOptionList['updatedDate'] === 'asc',
                'icon-selected': filterModel.sortBy === 'updatedDate'
              }"
            ></span>
          </div>
        </th>
        <th (click)="sort('createdBy', sortOptionList['createdBy'])" id="createdBy">
          <div class="d-flex align-items-center">
            Created By
            <span
              class="fa text-center cursor-pointer ms-auto"
              [ngClass]="{
                'fa-arrow-up': sortOptionList['createdBy'] === 'desc',
                'fa-arrow-down': sortOptionList['createdBy'] === 'asc',
                'icon-selected': filterModel.sortBy === 'createdBy'
              }"
            ></span>
          </div>
        </th>
        <th class="text-center min-width-action" id="action">Action</th>
      </tr>
    </thead>
    <tbody>
      <tr
        *ngFor="
          let formList of formsListingData
            | paginate
              : {
                  itemsPerPage: filterModel.itemsCount,
                  currentPage: currentPage,
                  totalItems: totalCount,
                  id: 'formListing'
                }
        "
      >
        <td data-title="Template Type">{{ formList.templateTypeName }}</td>
        <td data-title="Form Name">{{ formList.formName }}</td>
        <td data-title="Customer">{{ formList.customerName }}</td>
        <td data-title="version">
          <span *ngIf="formList.formVersion" nbTooltip="{{ formList?.formVersion }}" nbTooltipPlacement="top" nbTooltipStatus="primary">
            <sfl-read-more [content]="formList?.formVersion"></sfl-read-more>
          </span>
        </td>
        <td data-title="Template Name">{{ formList.templateName }}</td>
        <td data-title="Status" class="text-center">
          <nb-toggle
            status="primary"
            [(checked)]="formList.isActive"
            (checkedChange)="changeStatus(formList?.qestFormId, $event)"
            [disabled]="!(formList.customerName && formList.equipmentName)"
            *ngIf="userRole === 'admin' || userRole === 'commercialAssetsManager' || userRole === 'fieldTech'; else notadminandcam"
          ></nb-toggle>
          <ng-template #notadminandcam>
            {{ formList.isActive ? 'Active' : 'Inactive' }}
          </ng-template>
        </td>
        <td data-title="Last Updated On">
          {{ (formList.updatedDate | date : dateFormat) || '-' }}
        </td>
        <td data-title="Created By">{{ formList.createdBy }}</td>
        <td data-title="Action" class="text-center customer-action">
          <div>
            <em
              class="fa fa-copy text-primary cursor-pointer me-2"
              nbTooltip="Clone Form"
              nbTooltipPlacement="top"
              nbTooltipStatus="primary"
              (click)="cloneQESTForm(formList.qestFormId)"
              *ngIf="userRole === 'admin' || userRole === 'commercialAssetsManager' || userRole === 'fieldTech'"
            ></em>
            <em
              class="fa fa-eye cursor-pointer text-primary me-2"
              nbTooltip="View"
              nbTooltipPlacement="top"
              nbTooltipStatus="primary"
              (click)="customFormService.previewFormOrTemplate(formList.qestFormId, true)"
              aria-hidden="true"
              *ngIf="userRole === 'admin' || userRole === 'commercialAssetsManager' || userRole === 'fieldTech'"
            ></em>
            <em
              class="fa fa-edit cursor-pointer text-primary me-2"
              nbTooltip="Edit"
              nbTooltipPlacement="top"
              nbTooltipStatus="primary"
              aria-hidden="true"
              (click)="goToEditFormPage(formList.qestFormId)"
              *ngIf="userRole === 'admin' || userRole === 'commercialAssetsManager' || userRole === 'fieldTech'"
            ></em>
            <em
              class="fa fa-trash cursor-pointer text-danger"
              nbTooltip="Delete"
              nbTooltipPlacement="top"
              nbTooltipStatus="primary"
              aria-hidden="true"
              (click)="onFormDelete(formList.qestFormId)"
              *ngIf="userRole === 'admin' || userRole === 'commercialAssetsManager' || userRole === 'fieldTech'"
            ></em>
          </div>
        </td>
      </tr>
      <tr>
        <td colspan="9" *ngIf="!formsListingData?.length" class="no-record text-center">No Data Found</td>
      </tr>
    </tbody>
  </table>
</ng-template>

<ng-template #tpmFormTable>
  <table class="table table-hover table-bordered" aria-describedby="Tracker Preventative Maintenance Form List">
    <thead>
      <tr>
        <th (click)="sort('templateTypeName', sortOptionList['templateTypeName'])" id="templateTypeName">
          <div class="d-flex align-items-center">
            Template Type
            <span
              class="fa text-center cursor-pointer ms-auto"
              [ngClass]="{
                'fa-arrow-up': sortOptionList['templateTypeName'] === 'desc',
                'fa-arrow-down': sortOptionList['templateTypeName'] === 'asc',
                'icon-selected': filterModel.sortBy === 'templateTypeName'
              }"
            ></span>
          </div>
        </th>
        <th (click)="sort('formName', sortOptionList['formName'])" id="formName">
          <div class="d-flex align-items-center">
            Form Name
            <span
              class="fa text-center cursor-pointer ms-auto"
              [ngClass]="{
                'fa-arrow-up': sortOptionList['formName'] === 'desc',
                'fa-arrow-down': sortOptionList['formName'] === 'asc',
                'icon-selected': filterModel.sortBy === 'formName'
              }"
            ></span>
          </div>
        </th>
        <th class="text-center" id="Template Name">Template Name</th>
        <th class="text-center" id="Status">Status</th>
        <th (click)="sort('updatedDate', sortOptionList['updatedDate'])" id="updatedDate">
          <div class="d-flex align-items-center">
            Last Updated On
            <span
              class="fa text-center cursor-pointer ms-auto"
              [ngClass]="{
                'fa-arrow-up': sortOptionList['updatedDate'] === 'desc',
                'fa-arrow-down': sortOptionList['updatedDate'] === 'asc',
                'icon-selected': filterModel.sortBy === 'updatedDate'
              }"
            ></span>
          </div>
        </th>
        <th (click)="sort('createdBy', sortOptionList['createdBy'])" id="createdBy">
          <div class="d-flex align-items-center">
            Created By
            <span
              class="fa text-center cursor-pointer ms-auto"
              [ngClass]="{
                'fa-arrow-up': sortOptionList['createdBy'] === 'desc',
                'fa-arrow-down': sortOptionList['createdBy'] === 'asc',
                'icon-selected': filterModel.sortBy === 'createdBy'
              }"
            ></span>
          </div>
        </th>
        <th class="text-center min-width-action" id="action">Action</th>
      </tr>
    </thead>
    <tbody>
      <tr
        *ngFor="
          let formList of formsListingData
            | paginate
              : {
                  itemsPerPage: filterModel.itemsCount,
                  currentPage: currentPage,
                  totalItems: totalCount,
                  id: 'formListing'
                }
        "
      >
        <td data-title="Template Type">{{ formList.templateTypeName }}</td>
        <td data-title="Form Name">{{ formList.formName }}</td>
        <td data-title="Template Name">{{ formList.templateName }}</td>
        <td data-title="Status" class="text-center">
          <nb-toggle
            status="primary"
            [(checked)]="formList.isActive"
            (checkedChange)="changeStatus(formList?.qestFormId, $event)"
            *ngIf="userRole === 'admin' || userRole === 'commercialAssetsManager' || userRole === 'fieldTech'; else notadminandcam"
          ></nb-toggle>
          <ng-template #notadminandcam>
            {{ formList.isActive ? 'Active' : 'Inactive' }}
          </ng-template>
        </td>
        <td data-title="Last Updated On">
          {{ (formList.updatedDate | date : dateFormat) || '-' }}
        </td>
        <td data-title="Created By">{{ formList.createdBy }}</td>
        <td data-title="Action" class="text-center customer-action">
          <div>
            <em
              class="fa fa-copy text-primary cursor-pointer me-2"
              nbTooltip="Clone Form"
              nbTooltipPlacement="top"
              nbTooltipStatus="primary"
              (click)="cloneQESTForm(formList.qestFormId)"
              *ngIf="userRole === 'admin' || userRole === 'commercialAssetsManager' || userRole === 'fieldTech'"
            ></em>
            <em
              class="fa fa-eye cursor-pointer text-primary me-2"
              nbTooltip="View"
              nbTooltipPlacement="top"
              nbTooltipStatus="primary"
              (click)="customFormService.previewFormOrTemplate(formList.qestFormId, true)"
              aria-hidden="true"
              *ngIf="userRole === 'admin' || userRole === 'commercialAssetsManager' || userRole === 'fieldTech'"
            ></em>
            <em
              class="fa fa-edit cursor-pointer text-primary me-2"
              nbTooltip="Edit"
              nbTooltipPlacement="top"
              nbTooltipStatus="primary"
              aria-hidden="true"
              (click)="goToEditFormPage(formList.qestFormId)"
              *ngIf="userRole === 'admin' || userRole === 'commercialAssetsManager' || userRole === 'fieldTech'"
            ></em>
            <em
              class="fa fa-trash cursor-pointer text-danger"
              nbTooltip="Delete"
              nbTooltipPlacement="top"
              nbTooltipStatus="primary"
              aria-hidden="true"
              (click)="onFormDelete(formList.qestFormId)"
              *ngIf="userRole === 'admin' || userRole === 'commercialAssetsManager' || userRole === 'fieldTech'"
            ></em>
          </div>
        </td>
      </tr>
      <tr>
        <td colspan="9" *ngIf="!formsListingData?.length" class="no-record text-center">No Data Found</td>
      </tr>
    </tbody>
  </table>
</ng-template>

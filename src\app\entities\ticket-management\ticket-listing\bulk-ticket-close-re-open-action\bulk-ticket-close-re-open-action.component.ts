import { DatePipe } from '@angular/common';
import { Component, Input, OnInit, TemplateRef, ViewChild, ViewEncapsulation } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { BsModalRef, BsModalService, ModalOptions } from 'ngx-bootstrap/modal';
import { forkJoin, Subject, Subscription } from 'rxjs';
import { CommonFilter } from '../../../../@shared/components/filter/common-filter.model';
import { AppConstants } from '../../../../@shared/constants';
import { Dropdown } from '../../../../@shared/models/dropdown.model';
import { AlertService } from '../../../../@shared/services';
import { CommonService } from '../../../../@shared/services/common.service';
import { StorageService } from '../../../../@shared/services/storage.service';
import {
  CheckDeviceOutageForSelectedTicketsResponse,
  closeTicketTableFields,
  DeviceOutageForSelectedTicketsModalClickEnum,
  fieldNameMap,
  idMappings,
  NavigationBack,
  openTicketTableFields,
  TicketBillingStatusesResponse,
  TicketBulkCLoseReOpen,
  TicketPriorityMapping,
  Tickets,
  TicketsList
} from '../../ticket.model';
import { TicketService } from '../../ticket.service';

@Component({
  selector: 'sfl-bulk-ticket-close-re-open-action',
  templateUrl: './bulk-ticket-close-re-open-action.component.html',
  styleUrls: ['./bulk-ticket-close-re-open-action.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class BulkTicketCloseReOpenActionComponent implements OnInit {
  @Input() selectedBulkAction;
  @Input() filterModel: CommonFilter;
  @Input() total: number;
  @Input() isSingleTicketAction: boolean = false;
  @Input() ticketDetails: any = null;
  public onClose: Subject<boolean> = new Subject();
  pageSize = AppConstants.rowsPerPage;
  currentPage = 1;
  tickets: Tickets[];
  @ViewChild('truckRollModal') truckRollModal: TemplateRef<void>;
  @ViewChild('deviceOutageForSelectedTicketsModal') deviceOutageForSelectedTicketsModal: TemplateRef<void>;
  selectedBulkCloseReOpenTickets: Tickets[] = [];
  loading = false;
  selectedTicketForTruckRoll: Tickets;
  subscription: Subscription = new Subscription();
  sortOptionList = {
    Number: 'asc',
    Priority: 'asc',
    CustomerPortfolio: 'asc',
    Site: 'asc',
    Device: 'asc',
    Close: 'asc',
    RegionName: 'asc',
    SubRegionName: 'asc',
    Open: 'desc'
  };
  fullDateFormat = AppConstants.fullDateFormat;
  dateFormat = AppConstants.momentDateFormat;
  modalRef: BsModalRef;
  userRole = this.storageService.get('user').authorities;
  currentStep: number = 1;
  totalSteps: number = 5;
  isMasterSel = false;
  ticketCloseReOpenForm: TicketBulkCLoseReOpen = new TicketBulkCLoseReOpen();
  minDate: Date;
  ticketBillingStatuses: TicketBillingStatusesResponse[] = [];
  priorityList = TicketPriorityMapping;
  lossTypeList: Dropdown[] = [];
  fieldNameMap = fieldNameMap;
  idMappings = idMappings;
  completedCount = 0;
  failedEntityNumber = [];
  apiCount = 0;
  disabledResolved = false;
  fieldAddedForBulk = [
    { name: 'Issue', value: false, id: 1 },
    { name: 'Add Activity', value: false, id: 2 },
    { name: 'Add Comment', value: false, id: 3 }
  ];
  availableFields = {
    issue: false,
    activity: false,
    comment: false
  };
  invalidDeviceOutageTicketList: Tickets[] = [];
  deviceOutageForSelectedTicketsModalClickEnum = DeviceOutageForSelectedTicketsModalClickEnum;

  constructor(
    public _bsModalRef: BsModalRef,
    private readonly router: Router,
    private readonly route: ActivatedRoute,
    private readonly datePipe: DatePipe,
    private readonly commonService: CommonService,
    private readonly ticketService: TicketService,
    private readonly storageService: StorageService,
    private readonly modalService: BsModalService,
    private readonly alertService: AlertService
  ) {}

  ngOnInit(): void {
    if (this.isSingleTicketAction) {
      this.currentStep = 2;
      this.ticketCloseReOpenForm.isLinkTicket = true;
      this.selectedBulkCloseReOpenTickets.push(this.ticketDetails);
      this.disabledResolved = this.selectedBulkCloseReOpenTickets.some(ticket => ticket.isResolve);
      this.setCloseMinDate();
    } else {
      this.getAllTicketList();
    }
    const apiArray = [this.ticketService.getTicketBillingStatuses(), this.ticketService.getlossTypeList()];
    const tempObj = ['ticketBillingStatuses', 'lossTypeList'];
    forkJoin(apiArray).subscribe({
      next: (res: any) => {
        for (const [index, value] of tempObj.entries()) {
          this[value] = res[index];
        }
      },
      error: e => {}
    });
  }

  // isAnyFiledAdded() {
  //   return Object.values(this.availableFields).includes(true);
  // }

  addRemoveBulkEditFields(isChecked: boolean, fieldId): void {
    const ticketStatusField = this.fieldAddedForBulk.find(field => field.id === fieldId);
    if (ticketStatusField) {
      ticketStatusField.value = isChecked;
    }
    if (!isChecked) {
      switch (fieldId) {
        case 1:
          this.ticketCloseReOpenForm.issueTxt = '';
          break;
        case 2:
          this.ticketCloseReOpenForm.activityDate = null;
          this.ticketCloseReOpenForm.isResolved = false;
          this.ticketCloseReOpenForm.materials = '';
          this.ticketCloseReOpenForm.description = '';
          this.ticketCloseReOpenForm.materialCost = null;
          break;
        case 3:
          this.ticketCloseReOpenForm.comment = '';
          break;

        default:
          break;
      }
    }
  }

  getClosedTableData() {
    return Object.keys(this.ticketCloseReOpenForm || {})
      .filter(key => this.shouldIncludeKey(key, closeTicketTableFields))
      .map(key => ({
        fieldName: this.fieldNameMap[key] || key,
        value: this.getDisplayName(key, this.ticketCloseReOpenForm[key]),
        action: key === 'comment' ? 'Added' : 'Updated'
      }))
      .filter(item => this.isValidValue(item.value));
  }

  getOpenedTableData() {
    return Object.keys(this.ticketCloseReOpenForm || {})
      .filter(key => this.shouldIncludeKey(key, openTicketTableFields))
      .map(key => ({
        fieldName: this.issueKeyNameChange(key),
        value: this.getDisplayName(key, this.ticketCloseReOpenForm[key]),
        action: key === 'comment' ? 'Added' : 'Updated'
      }))
      .filter(item => this.isValidValue(item.value));
  }

  private shouldIncludeKey(key: string, excludedKeys: string[]): boolean {
    if (key === 'isResolved') {
      return this.availableFields?.activity;
    }
    return !excludedKeys.includes(key);
  }

  private issueKeyNameChange(key: string): string {
    if (key === 'newTicketIssueTxt') {
      return this.ticketCloseReOpenForm.issueActionClose === 2
        ? 'Use Closed Ticket Issue and Append Text'
        : 'Overwrite/New Ticket Issue Text';
    }
    return this.fieldNameMap[key] || key;
  }

  private isValidValue(value: any): boolean {
    return value !== null && value !== '' && value !== undefined;
  }

  getDisplayName(fieldName: string, fieldValue: any): string {
    if (this.idMappings[fieldName] && this.idMappings[fieldName][fieldValue] !== undefined) {
      return this.idMappings[fieldName][fieldValue];
    } else if (fieldName === 'billingStatus') {
      return this.getBillingStatusDescription(fieldValue);
    } else if (fieldName === 'lossType') {
      return this.getLossTypeName(fieldValue);
    } else if (fieldName === 'closedDate' || fieldName === 'activityDate' || fieldName === 'openedDate') {
      return this.datePipe.transform(fieldValue, AppConstants.fullDateFormat);
    }
    return fieldValue;
  }

  getBillingStatusDescription(statusId: number) {
    const billingStatus = this.ticketBillingStatuses.find(status => status.ticketBillingStatusID === statusId);
    return billingStatus ? billingStatus.description : '';
  }
  getLossTypeName(lossTypeId: number) {
    const billingStatus = this.lossTypeList.find(status => status.id === lossTypeId);
    return billingStatus ? billingStatus.name : '';
  }

  changesIssueAction() {
    if (this.ticketCloseReOpenForm.issueActionClose === 1) {
      this.ticketCloseReOpenForm.newTicketIssueTxt = '';
    }
  }

  SaveBulkCloseReOpenAction() {
    this.currentStep = 5;
    const ticketIds = this.selectedBulkCloseReOpenTickets.map(item => item.id);
    const requestModel = {
      ticketIds: ticketIds,
      closedDate: this.datePipe.transform(this.ticketCloseReOpenForm.closedDate, AppConstants.fullDateFormat),
      billingStatus: this.ticketCloseReOpenForm.billingStatus,
      issueTxt: this.ticketCloseReOpenForm.issueTxt,
      activity: this.availableFields.activity
        ? {
            activityDate: this.datePipe.transform(this.ticketCloseReOpenForm.activityDate, AppConstants.fullDateFormat),
            description: this.ticketCloseReOpenForm.description,
            isResolved: this.ticketCloseReOpenForm.isResolved,
            materials: this.ticketCloseReOpenForm.materials,
            materialCost: this.ticketCloseReOpenForm.materialCost
          }
        : null,
      comment: this.ticketCloseReOpenForm.comment,
      isLinkTicket: this.ticketCloseReOpenForm.isLinkTicket,
      OpenedDate: this.datePipe.transform(this.ticketCloseReOpenForm.openedDate, AppConstants.fullDateFormat),
      issueAction: this.ticketCloseReOpenForm.issueActionClose,
      newTicketIssueTxt: this.ticketCloseReOpenForm.newTicketIssueTxt,
      isSendEmail: this.ticketCloseReOpenForm.isSendEmail
    };
    this.subscription.add(
      this.ticketService.saveBulkTicketCLoseReOpenAction(requestModel).subscribe({
        next: res => {
          if (res.status === 1 || res.status === -1) {
            this.apiCount = this.selectedBulkCloseReOpenTickets.length;
            this.completedCount = res.entryid;
            res.status === 1 ? this.alertService.showSuccessToast(res.message) : this.alertService.showErrorToast(res.message);
            if (res.entityIds.length > 0) {
              this.failedEntityNumber = this.selectedBulkCloseReOpenTickets
                .filter(item => res.entityIds.includes(item.id))
                .map(item => item.ticketNumber);
            }
          } else {
            this.updateSingleTicket(requestModel);
          }
        },
        error: e => {
          this.loading = false;
        }
      })
    );
  }

  updateSingleTicket(requestModel) {
    const failedId = [];
    for (const element of this.selectedBulkCloseReOpenTickets) {
      const singleModel = {
        ...requestModel,
        ticketIds: [element.id]
      };
      this.subscription.add(
        this.ticketService.saveBulkTicketCLoseReOpenAction(singleModel).subscribe({
          next: res => {
            this.apiCount++;
            if (res.status === 1) {
              this.completedCount++;
            } else {
              if (res.entityIds.length > 0) {
                this.failedEntityNumber.push(res.entityIds);
              }
            }
            if (this.completedCount === this.selectedBulkCloseReOpenTickets.length) {
              this.alertService.showSuccessToast('Bulk Ticket Update Successfully.');
            }
          },
          error: e => {
            this.loading = false;
          }
        })
      );
    }
    if (failedId.length > 0) {
      this.failedEntityNumber = this.selectedBulkCloseReOpenTickets
        .filter(item => failedId.includes(item.id))
        .map(item => item.ticketNumber);
    }
  }

  public onCancel(): void {
    this.onClose.next(true);
    this._bsModalRef.hide();
  }

  closeModel() {
    this._bsModalRef.hide();
    this.selectedBulkCloseReOpenTickets = [];
    this.ticketCloseReOpenForm = new TicketBulkCLoseReOpen();
  }

  // openDateChanged() {
  //   if (this.ticketCloseReOpenForm.openedDate) {
  //     this.minDate = new Date(this.ticketCloseReOpenForm.openedDate);
  //     this.minDate.setHours(0, 0, 0, 0); // Sets the time to 00:00:00.000
  //   }
  // }

  setCloseMinDate() {
    this.ticketCloseReOpenForm.closedDate = null;
    this.minDate = new Date(Math.max(...this.selectedBulkCloseReOpenTickets.map(ticket => new Date(ticket.open).getTime())));
  }

  proceedNextStep(bulkCloseReOpenForm) {
    // Validate form on the current step
    if (this.currentStep === 2) {
      if (bulkCloseReOpenForm?.form?.valid) {
        this.moveToNextStep();
        this.checkDeviceOutageForSelectedTickets();
      } else {
        this.markFormControlsAsTouched(bulkCloseReOpenForm);
        return;
      }
    } else {
      if (this.currentStep == 1) {
        this.disabledResolved = this.selectedBulkCloseReOpenTickets.some(ticket => ticket.isResolve);
        this.setCloseMinDate();
      }
      this.moveToNextStep();
    }
  }

  moveToNextStep() {
    if (this.currentStep < this.totalSteps) {
      this.currentStep++;
    }
  }

  markFormControlsAsTouched(form) {
    if (form && form.form && form.form.controls) {
      Object.keys(form.form.controls).forEach(key => {
        const control = form.form.controls[key];
        control.markAsTouched();
        control.updateValueAndValidity();
      });
    }
  }

  proceedBackStep() {
    const minimumStep = this.isSingleTicketAction ? 2 : 1;
    if (this.currentStep > minimumStep) {
      this.currentStep--;
    } else {
      this.closeModel();
    }
  }

  checkDeviceOutageForSelectedTickets(): void {
    const ticketIds = this.selectedBulkCloseReOpenTickets.map(item => item.id);
    const requestModel = {
      ticketIds: ticketIds,
      closedDate: this.datePipe.transform(this.ticketCloseReOpenForm.closedDate, AppConstants.fullDateFormat)
    };
    this.loading = true;
    this.ticketService.checkDeviceOutageForSelectedTickets(requestModel).subscribe({
      next: (res: CheckDeviceOutageForSelectedTicketsResponse) => {
        if (res?.ticketIds && res?.ticketIds?.length > 0) {
          this.openDeviceOutageForSelectedTicketsModal(res?.ticketIds);
        }
        this.loading = false;
      },
      error: e => {
        this.loading = false;
      }
    });
  }

  openDeviceOutageForSelectedTicketsModal(invalidTicketIds: number[]) {
    const invalidTickets = this.selectedBulkCloseReOpenTickets.filter(ticket => invalidTicketIds.includes(ticket.id));
    this.invalidDeviceOutageTicketList = invalidTickets;

    const ngModalOptions: ModalOptions = {
      backdrop: true,
      keyboard: false,
      animated: true,
      class: 'modal-lg open-device-outage-for-selected-tickets-modal',
      ignoreBackdropClick: true
    };

    this.modalRef = this.modalService.show(this.deviceOutageForSelectedTicketsModal, ngModalOptions);
  }

  onDeviceOutageForSelectedTicketsModalClick(
    deviceOutageForSelectedTicketsModalClickEnum: DeviceOutageForSelectedTicketsModalClickEnum
  ): void {
    switch (deviceOutageForSelectedTicketsModalClickEnum) {
      case DeviceOutageForSelectedTicketsModalClickEnum.CLOSE:
      case DeviceOutageForSelectedTicketsModalClickEnum.CANCEL:
        if (this.isSingleTicketAction) {
          this.onCancel();
        } else {
          this.currentStep = 1;
        }
        this.modalRef?.hide();
        break;
      case DeviceOutageForSelectedTicketsModalClickEnum.CHANGE_CLOSE_DATE:
        this.modalRef?.hide();
        this.invalidDeviceOutageTicketList = [];
        this.setCloseMinDate();
        this.proceedBackStep();
        break;
      case DeviceOutageForSelectedTicketsModalClickEnum.CONTINUE:
        this.continueWithOmittedSelectedTickets();
        break;
    }
  }

  continueWithOmittedSelectedTickets(): void {
    const invalidTickets = this.invalidDeviceOutageTicketList.map(ticket => ticket.id);
    this.selectedBulkCloseReOpenTickets = this.selectedBulkCloseReOpenTickets.filter(ticket => !invalidTickets.includes(ticket.id));
    this.invalidDeviceOutageTicketList = [];
    this.modalRef?.hide();
  }

  sort(sortBy: string, changeSort: string) {
    if (changeSort === 'asc') {
      changeSort = 'desc';
    } else {
      changeSort = 'asc';
    }
    this.sortOptionList[sortBy] = changeSort;
    this.filterModel.sortBy = sortBy;
    this.filterModel.direction = changeSort;
    this.currentPage = 1;
    this.getAllTicketList();
  }

  openTruckRollModal(item: Tickets, template: TemplateRef<void>) {
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      class: 'modal-md'
    };
    this.selectedTicketForTruckRoll = item;
    this.modalRef = this.modalService.show(template, ngModalOptions);
  }

  onPageChange(obj) {
    this.currentPage = obj;
  }

  getAllTicketList() {
    this.loading = true;
    const model: CommonFilter = JSON.parse(JSON.stringify(this.filterModel));
    if (this.filterModel.openDate && this.filterModel.openDate.start && this.filterModel.openDate.end) {
      model.openDate.start = this.datePipe.transform(this.filterModel.openDate.start, AppConstants.fullDateFormat);
      model.openDate.end = this.datePipe.transform(this.filterModel.openDate.end, AppConstants.fullDateFormat);
    } else {
      this.filterModel.openDate = null;
      model.openDate = null;
    }
    if (this.filterModel.closeDate && this.filterModel.closeDate.start && this.filterModel.closeDate.end) {
      model.closeDate.start = this.datePipe.transform(this.filterModel.closeDate.start, AppConstants.fullDateFormat);
      model.closeDate.end = this.datePipe.transform(this.filterModel.closeDate.end, AppConstants.fullDateFormat);
    } else {
      this.filterModel.closeDate = null;
      model.closeDate = null;
    }
    if (this.filterModel.activityRange && this.filterModel.activityRange.start && this.filterModel.activityRange.end) {
      model.activityRange.start = this.datePipe.transform(this.filterModel.activityRange.start, AppConstants.fullDateFormat);
      model.activityRange.end = this.datePipe.transform(this.filterModel.activityRange.end, AppConstants.fullDateFormat);
    } else {
      this.filterModel.activityRange = null;
      model.activityRange = null;
    }
    model.statusIds = [1, 2];
    model.itemsCount = 1000;
    model.getAllStatusListBetweenDates = false;

    this.ticketService.getAllTicketList(model).subscribe({
      next: (res: TicketsList) => {
        this.bindTickets(res);
      },
      error: e => {
        this.loading = false;
      }
    });
  }

  bindTickets(res: TicketsList) {
    if (!res?.ticketLists) {
      this.tickets = [];
      this.total = 0;
      this.isMasterSel = false;
      this.disabledResolved = false;
      this.loading = false;
      return;
    }

    this.tickets = res.ticketLists.map(ticket => ({
      ...ticket,
      show: false,
      isSelected: this.selectedBulkCloseReOpenTickets.some(selected => selected.id === ticket.id)
    }));

    this.total = this.tickets?.length;
    this.isMasterSel = this.tickets.length > 0 && this.tickets.every(ticket => ticket.isSelected);
    this.loading = false;
  }

  selectDeselectAll() {
    const selectedIds = new Set(this.selectedBulkCloseReOpenTickets.map(file => file.id));

    this.tickets.forEach(ticket => {
      ticket.isSelected = this.isMasterSel;

      if (this.isMasterSel) {
        if (!selectedIds.has(ticket.id)) {
          this.selectedBulkCloseReOpenTickets.push(ticket);
        }
      } else {
        this.selectedBulkCloseReOpenTickets = this.selectedBulkCloseReOpenTickets.filter(file => file.id !== ticket.id);
      }
    });
  }

  singleTicketCheckChanged(ticket: Tickets) {
    if (ticket.isSelected) {
      if (!this.selectedBulkCloseReOpenTickets.some(file => file.id === ticket.id)) {
        this.selectedBulkCloseReOpenTickets.push(ticket);
      }
    } else {
      this.selectedBulkCloseReOpenTickets = this.selectedBulkCloseReOpenTickets.filter(file => file.id !== ticket.id);
    }

    this.isMasterSel = this.tickets.every(ticket => ticket.isSelected);
  }

  openLink(ticketNumber: string, inNewWindow: boolean) {
    const url = this.router.serializeUrl(
      this.router.createUrlTree(['../entities/ticket/detail/view', `${ticketNumber}`], { queryParams: { back: NavigationBack.TICKETS } })
    );
    if (inNewWindow) {
      window.open(url, '_blank', 'width=' + screen.availWidth + ',height=' + screen.availHeight);
    } else {
      window.open(url, '_blank');
    }
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}

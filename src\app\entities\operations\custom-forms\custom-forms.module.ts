import { DragDropModule } from '@angular/cdk/drag-drop';
import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { SharedModule } from '../../../@shared/shared.module';
import { AddEditTemplateComponent } from './add-edit-template/add-edit-template.component';
import { CustomFormsLandingPageComponent } from './custom-forms-landing-page/custom-forms-landing-page.component';
import { CustomFormsRoutingModule } from './custom-forms-routing.module';
import { CustomFormsComponent } from './custom-forms.component';
import { HowToUseVideoModalComponent } from './how-to-use-video-modal/how-to-use-video-modal.component';
import { MyFormsListingPageComponent } from './my-forms-listing-page/my-forms-listing-page.component';
import { PreviewFormTemplateModalComponent } from './preview-form-template-modal/preview-form-template-modal.component';
import { TemplateListingComponent } from './template-listing/template-listing.component';
import { FormAnalyticsComponent } from './form-analytics/form-analytics.component';
import { AddEditFormTagsComponent } from './form-analytics/add-edit-form-tags/add-edit-form-tags.component';
@NgModule({
  imports: [CommonModule, SharedModule, CustomFormsRoutingModule, DragDropModule],
  declarations: [
    CustomFormsComponent,
    CustomFormsLandingPageComponent,
    TemplateListingComponent,
    HowToUseVideoModalComponent,
    AddEditTemplateComponent,
    MyFormsListingPageComponent,
    PreviewFormTemplateModalComponent,
    FormAnalyticsComponent,
    AddEditFormTagsComponent
  ]
})
export class CustomFormsModule {}

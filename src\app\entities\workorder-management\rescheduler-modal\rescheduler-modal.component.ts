import { Component, Input, On<PERSON><PERSON>roy, OnInit, TemplateRef } from '@angular/core';
import * as _moment from 'moment/moment';
import { BsModalRef, BsModalService, ModalOptions } from 'ngx-bootstrap/modal';
import { Subject, Subscription } from 'rxjs';
import { AppConstants } from '../../../@shared/constants';
import { UserRole, UserRoleIdMap } from '../../../@shared/models/user.model';
import { AlertService } from '../../../@shared/services';
import { DashboardService } from '../../dashboard/dashboard.service';
import { PMTableStatus } from '../../operations/operations-reports/operation-reports.model';
import { BulkRescheduleDetails, BulkRescheduleResponse, RescheduleForm } from '../workorder.model';
import { WorkorderService } from '../workorder.service';

@Component({
  selector: 'sfl-rescheduler-modal',
  templateUrl: './rescheduler-modal.component.html',
  styleUrls: ['./rescheduler-modal.component.scss']
})
export class ReschedulerModalComponent implements OnInit, OnDestroy {
  @Input() primaryFieldtTechdata;
  @Input() workOrderData; // adding a new reschedule
  @Input() rescheduleDetails; // when editing a reschedule
  @Input() bulkRescheduleDetails: BulkRescheduleDetails; // when bulk rescheduling
  public onClose: Subject<boolean> = new Subject();
  moment = (_moment as any).default ? (_moment as any).default : _moment;
  subscription: Subscription = new Subscription();
  loading = false;
  minDate = new Date();
  rescheduleForm: RescheduleForm = new RescheduleForm();
  categories = [
    { id: 1, name: 'Corrective Maintenance - Outage' },
    { id: 2, name: 'Corrective Maintenance - Emergency' },
    { id: 3, name: 'Corrective Maintenance - Client Request' },
    { id: 4, name: 'Equipment Access Restriction' },
    { id: 5, name: 'Lack of Preparation' },
    { id: 6, name: 'Site Access Restriction' },
    { id: 7, name: 'Site Conditions - Flooding' },
    { id: 8, name: 'Site Conditions - Overgrown' },
    { id: 9, name: 'Site Conditions - Snow' },
    { id: 10, name: 'Tech Availability' },
    { id: 11, name: 'Tech Sick Day / PTO' },
    { id: 12, name: 'Thermal Event (Fire/Smoke)' },
    { id: 13, name: 'Weather - Rain' },
    { id: 14, name: 'Weather - Snow' },
    { id: 15, name: 'Weather - Clouds / Low Irradiance' }
  ];
  currentSchedule = new Date();
  isWoPendingRescheduled = false;
  modalRef: BsModalRef;
  bulkRescheduleResponse: BulkRescheduleResponse[];
  bulkRescheduleResponseMessage: string;
  selectedWOReschedules: any[] = [];
  constructor(
    private readonly _bsReschedulerModalRef: BsModalRef,
    private readonly workOrderService: WorkorderService,
    private readonly alertService: AlertService,
    private readonly dashboardService: DashboardService,
    private readonly modalService: BsModalService,
    private readonly workorderService: WorkorderService
  ) {}

  async ngOnInit(): Promise<void> {
    if (!this.bulkRescheduleDetails) await this.getAllReschedulesByWorkOrder(this.workOrderData.id);
    if (this.rescheduleDetails) {
      this.isWoPendingRescheduled = !(this.rescheduleDetails.scheduleDate && this.rescheduleDetails.currentScheduleDate);
      this.currentSchedule = this.rescheduleDetails.scheduleDate
        ? new Date(this.rescheduleDetails.scheduleDate)
        : this.rescheduleDetails.currentScheduleDate
        ? new Date(this.rescheduleDetails.currentScheduleDate)
        : null;
      this.initializeWorkOrderRescheduleForm();
    } else if (this.bulkRescheduleDetails) {
      this.isWoPendingRescheduled = this.bulkRescheduleDetails.WoStatus === PMTableStatus['Pending Reschedule'];
      this.currentSchedule = null;
    } else if (
      this.selectedWOReschedules.length &&
      !this.selectedWOReschedules[this.selectedWOReschedules.length - 1]?.scheduleDate &&
      (this.workOrderData.rescheduleCount === undefined || this.workOrderData.rescheduleCount === 0)
    ) {
      this.isWoPendingRescheduled = true;
      this.rescheduleForm.workOrderId = this.workOrderData.id;
    } else {
      if (
        this.workOrderData.rescheduleCount === undefined ||
        this.workOrderData.rescheduleCount === 0 ||
        this.workOrderData.rescheduleCount === '-'
      ) {
        this.isWoPendingRescheduled = false;
      } else {
        this.isWoPendingRescheduled = !((this.workOrderData.dateReScheduled ?? '-') !== '-' || this.workOrderData.rescheduleDate);
      }
      const dateScheduledIsNotFromWOSchedule =
        this.workOrderData?.workOrderSchedules && this.workOrderData?.workOrderSchedules.length > 0
          ? this.workOrderData.workOrderSchedules.find(item => {
              return (
                new Date(item.currentScheduleDate).valueOf() === new Date(this.workOrderData.dateScheduled).valueOf() && item.scheduleDate
              );
            })
          : true;
      this.currentSchedule =
        this.workOrderData.dateReScheduled && this.workOrderData.dateReScheduled !== '-'
          ? new Date(this.workOrderData.dateReScheduled)
          : this.workOrderData.rescheduleDate
          ? new Date(this.workOrderData.rescheduleDate)
          : this.workOrderData.dateScheduled && !dateScheduledIsNotFromWOSchedule
          ? new Date(this.workOrderData.dateScheduled)
          : new Date();
      this.workOrderData.dateReScheduled = this.workOrderData.dateReScheduled ? new Date(this.workOrderData.dateReScheduled) : '';
      this.workOrderData.dateScheduled = this.workOrderData.dateScheduled ? new Date(this.workOrderData.dateScheduled) : null;
      this.rescheduleForm.workOrderId = this.workOrderData.id;
    }

    this.minDate = new Date(this.minDate.setHours(0, 0, 0, 0));
    if (!this.primaryFieldtTechdata?.length) {
      if (this.bulkRescheduleDetails) {
        this.getAllFieldTechUsers();
      } else {
        this.getFieldTechUsers();
      }
    }
  }

  initializeWorkOrderRescheduleForm() {
    this.rescheduleForm.id = this.rescheduleDetails.id;
    this.rescheduleForm.fieldTech = this.rescheduleDetails.fieldTech?.split(',').map(Number);
    this.rescheduleForm.resasonForReschedule = this.rescheduleDetails.resasonForReschedule;
    this.rescheduleForm.scheduleDate = this.currentSchedule;
    this.rescheduleForm.workOrderId = this.rescheduleDetails.workOrderId;
    this.rescheduleForm.notes = this.rescheduleDetails.notes;
  }

  getFieldTechUsers() {
    const userRoles = Object.entries(UserRoleIdMap)
      .filter(([roleKey, id]) => roleKey !== UserRole.CUST && roleKey !== UserRole.ANL)
      .map(([_, id]) => id);
    this.subscription.add(
      this.workOrderService.getUserByRoles(userRoles).subscribe({
        next: res => {
          this.primaryFieldtTechdata = res;
        }
      })
    );
  }

  getAllFieldTechUsers() {
    this.subscription.add(
      this.workOrderService.getAllFieldTechForReschedule().subscribe({
        next: res => {
          this.primaryFieldtTechdata = res;
        }
      })
    );
  }

  // Filter function for nb-datepicker
  dateFilter = (date: Date): boolean => {
    const today = this.minDate;
    const selectedDate = this.currentSchedule;
    if (!this.isWoPendingRescheduled && isNaN(date.getDay())) {
      return true;
    }
    // Disable all previous days, excluding the currently selected day and allow null
    return (date >= selectedDate && date <= selectedDate) || date >= today;
  };

  reschedule(modalTemplate?: TemplateRef<void>) {
    this.loading = true;
    let today = new Date(new Date().setHours(0, 0, 0, 0));
    const request: RescheduleForm = {
      id: this.rescheduleForm.id ?? undefined,
      workOrderId: this.rescheduleForm?.workOrderId,
      workOrderIds: this.bulkRescheduleDetails?.selectedWoIds ?? [],
      fieldTech: this.rescheduleForm.fieldTech?.toString(),
      scheduleDate:
        this.rescheduleForm.scheduleDate >= today
          ? this.moment(this.rescheduleForm.scheduleDate).format(AppConstants.momentDateFormat)
          : null,
      resasonForReschedule: this.rescheduleForm.resasonForReschedule,
      notes: this.rescheduleForm.notes
    };
    const rescheduleObservable = this.bulkRescheduleDetails
      ? this.workOrderService.bulkUpdateWorkOrderReschedule(request)
      : this.workOrderService.addUpdateWorkOrderReschedule(request);

    this.subscription.add(
      rescheduleObservable.subscribe({
        next: res => {
          if (typeof request.workOrderId === 'number') {
            this.dashboardService.updatedWODetails = {
              workOrderId: request.workOrderId,
              scheduledDate: request.scheduleDate,
              siteId: this.workOrderData.siteId,
              tentativeMonth: this.workOrderData.tentativeMonth
            };
            this.dashboardService.reloadWorkOrder$.next(request.workOrderId);
          }
          this.loading = false;
          // 3780 update the toaster to show modal popup so that user can take a look at the details
          if (this.bulkRescheduleDetails && modalTemplate && res?.failedWorkOrders?.length) {
            this.bulkRescheduleResponse = res.failedWorkOrders;
            this.bulkRescheduleResponseMessage = res.message;
            const ngModalOptions: ModalOptions = {
              backdrop: 'static',
              keyboard: false,
              animated: true,
              class: 'modal-lg'
            };
            this.onHide(true);
            this.modalRef = this.modalService.show(modalTemplate, ngModalOptions);
            this.modalRef.onHide.subscribe({
              next: () => {
                this.bulkRescheduleResponse = [];
                this.bulkRescheduleResponseMessage = '';
              }
            });
          } else {
            this.alertService.showSuccessToast('The Work Order was rescheduled successfully.');
            this.onHide(true);
          }
        },
        error: () => (this.loading = false)
      })
    );
  }

  onHide(val: boolean) {
    this.onClose.next(val);
    this._bsReschedulerModalRef.hide();
  }

  lockUnlockWO(data, isLocked = false) {
    this.loading = true;

    if (!isLocked) {
      this.workOrderService.lockWO(data).subscribe({
        next: res => {
          this.loading = false;
        },
        error: () => (this.loading = false)
      });
    } else {
      this.workOrderService.unlockWO(data).subscribe({
        next: res => {
          this.loading = false;
        },
        error: () => (this.loading = false)
      });
    }
  }

  getAllReschedulesByWorkOrder(id): Promise<void> {
    return new Promise((resolve, reject) => {
      this.loading = true;
      this.subscription.add(
        this.workorderService.getWorkOrderReschedules(id).subscribe({
          next: reschedules => {
            if (reschedules?.length) {
              this.selectedWOReschedules = reschedules;
            }
            this.loading = false;
            resolve();
          },
          error: () => {
            this.loading = false;
            reject();
          }
        })
      );
    });
  }

  ngOnDestroy() {
    this.lockUnlockWO(
      {
        woId: this.rescheduleForm.workOrderId,
        isReLockWo: false
      },
      true
    );
    this.subscription.unsubscribe();
  }
}

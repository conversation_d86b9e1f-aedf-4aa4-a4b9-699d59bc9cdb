import { Location } from '@angular/common';
import { Component, OnInit, TemplateRef } from '@angular/core';
import { NgForm } from '@angular/forms';
import { Router } from '@angular/router';
import { BsModalRef, BsModalService, ModalOptions } from 'ngx-bootstrap/modal';
import { Subscription } from 'rxjs';
import { ConfirmDialogComponent } from '../../../../@shared/components/confirm-dialog/confirm-dialog.component';
import { CommonFilter } from '../../../../@shared/components/filter/common-filter.model';
import { FILTER_SECTION_ENUM, FilterDetails } from '../../../../@shared/components/filter/filter.model';
import { AppConstants } from '../../../../@shared/constants';
import { AlertService } from '../../../../@shared/services';
import { StorageService } from '../../../../@shared/services/storage.service';
import { CustomFormService } from '../custom-form.service';
import { FormsListingData, FormsListingResponse, QESTFormTemplateTypes } from '../custom-forms.model';
@Component({
  selector: 'sfl-my-forms-listing-page',
  templateUrl: './my-forms-listing-page.component.html',
  styleUrls: ['./my-forms-listing-page.component.scss']
})
export class MyFormsListingPageComponent implements OnInit {
  subscription: Subscription = new Subscription();
  formsListingData: FormsListingData[] = [];
  loading = false;
  filterDetails: FilterDetails = new FilterDetails();
  isFilterDisplay = false;
  filterModel: CommonFilter = new CommonFilter();
  dateFormat = AppConstants.fullDateFormat;
  pageSize = AppConstants.rowsPerPage;
  totalCount: number = 0;
  totalActivatedReports: number = 0;
  currentPage = 1;
  sortOptionList = {
    templateTypeName: 'asc',
    formName: 'asc',
    updatedDate: 'asc',
    createdBy: 'asc'
  };
  modalRef: BsModalRef;
  viewPage = 'customFormListingPage';
  viewFilterSection = 'customFormListingFilterSection';
  templateDropdownData: any[] = [];
  userRole: string;
  qestFormTemplateTypes = QESTFormTemplateTypes;
  currrentTemplateType;
  templateTypesList = [];
  isCoverPageTemplate = false;

  constructor(
    private readonly router: Router,
    private readonly location: Location,
    private readonly alertService: AlertService,
    private readonly modalService: BsModalService,
    private readonly storageService: StorageService,
    public readonly customFormService: CustomFormService
  ) {}

  ngOnInit() {
    this.userRole = this.storageService.get('user').authorities[0];
    const filter = this.storageService.get(this.viewPage),
      filterSection = this.storageService.get(this.viewFilterSection),
      sharedFilter = this.storageService.get(AppConstants.SHARED_FILTER_KEY);

    if (filter) {
      this.filterModel = filter;
      this.pageSize = this.filterModel.itemsCount;
      this.currentPage = this.filterModel.page + 1;
      this.currrentTemplateType = this.filterModel.templateTypeId;
      if (this.filterModel.direction && this.filterModel.sortBy) {
        this.sortOptionList[this.filterModel.sortBy] = this.filterModel.direction;
      }
    } else {
      this.filterModel.templateTypeId = this.currrentTemplateType;
      this.storageService.set(this.viewPage, this.filterModel);
    }

    this.initFilterDetails();

    this.filterModel.itemsCount = this.pageSize;
    this.filterModel.page = this.currentPage - 1;
    this.filterModel.sortBy = 'updatedDate';
    this.isFilterDisplay = filterSection;
    this.filterModel.customerIds = sharedFilter?.customerIds.length ? sharedFilter.customerIds : this.filterModel.customerIds || [];
    this.filterModel.portfolioIds = [];
    this.filterModel.siteIds = [];
    const operationCustomFormsFilterKeys = ['customerIds', 'search', 'templateTypeId', 'equipmentIds'];
    if (this.storageService.shouldCallListApi(filter, {}, {}, this.filterModel, operationCustomFormsFilterKeys)) {
      this.getFormsList();
    } else {
      this.getTemplateDropdownList();
    }
  }

  initFilterDetails(): void {
    this.filterDetails.filter_section_name = this.viewFilterSection;
    this.filterDetails.filterSectionEnum = FILTER_SECTION_ENUM.OPERATIONS_CUSTOM_FORMS_FORM_LISTING;
    this.filterDetails.page_name = this.viewPage;
    this.filterDetails.api = [];
    let filterItem = JSON.parse(JSON.stringify(AppConstants.FILTERS));
    filterItem.SEARCH_BOX.show = true;
    filterItem.TEMPLATE_TYPE_FORM_LIST_IDS.show = true;
    filterItem.EQUIPMENT_LIST.show = true;
    filterItem.CUSTOMER.show = true;
    filterItem.CUSTOMER.multi = true;
    this.filterDetails.default_sort = 'updatedDate';
    this.filterDetails.filter_item = filterItem;
  }

  sort(sortBy: string, changeSort: string) {
    if (changeSort === 'asc') {
      changeSort = 'desc';
    } else {
      changeSort = 'asc';
    }

    this.sortOptionList[sortBy] = changeSort;
    this.filterModel.sortBy = sortBy;
    this.filterModel.direction = changeSort;

    this.getFormsList();
  }

  getFormsList(saveFilter = true, filterParams?: CommonFilter): void {
    this.loading = true;

    if (filterParams) {
      this.filterModel = filterParams;
    }

    if (saveFilter) {
      this.storageService.set(this.viewPage, this.filterModel);
    }

    this.subscription.add(
      this.customFormService.getFormsListing(this.filterModel).subscribe({
        next: (data: FormsListingResponse) => {
          this.formsListingData = data.listOfQESTForm;
          this.totalCount = data.totalQESTForm;
          this.totalActivatedReports = data?.totalActivatedReports ?? 0;
          this.getTemplateDropdownList();
        },
        error: () => (this.loading = false)
      })
    );
  }

  getTemplateDropdownList() {
    this.subscription.add(
      this.customFormService.getTemplateDropdownList().subscribe({
        next: (response: any) => {
          this.templateDropdownData = response;
          this.loading = false;
        },
        error: () => (this.loading = false)
      })
    );
  }

  openConfirmationModal(formName): Promise<boolean> {
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      initialState: {
        confirmBtnText: 'Yes',
        cancelBtnText: 'No',
        message: `Do you want to make this, the active ${formName} form?`
      }
    };
    this.modalRef = this.modalService.show(ConfirmDialogComponent, ngModalOptions);
    return new Promise(res => {
      this.modalRef.content.onClose.subscribe(result => {
        res(result);
        return;
      });
    });
  }

  async changeStatus(formId: number, event) {
    switch (this.currrentTemplateType) {
      case QESTFormTemplateTypes.QEST_INVERTER_PM:
      case QESTFormTemplateTypes.QEST_MODULE_TORQUE:
        this.changeFormStatus(formId);
        break;

      case QESTFormTemplateTypes.QEST_SUMMARY_REPORT:
        const selectedFromItem = this.formsListingData.find(item => item.qestFormId === formId);
        if (event) {
          if (this.totalActivatedReports !== 0) {
            const isStatusChangeDiscard = await this.openConfirmationModal('summary report');
            if (isStatusChangeDiscard) {
              this.changeFormStatus(formId, true);
            } else {
              selectedFromItem.isActive = !event;
            }
          } else {
            this.changeFormStatus(formId, true);
          }
        } else {
          this.changeFormStatus(formId, true);
        }
        break;

      case QESTFormTemplateTypes.QEST_COVER_PAGE:
        const selectedFormToggle = this.formsListingData.find(item => item.qestFormId === formId);
        if (event) {
          const isStatusChangeDiscard = await this.openConfirmationModal('cover page');
          if (isStatusChangeDiscard) {
            this.changeFormStatus(formId, true);
          } else {
            selectedFormToggle.isActive = !event;
          }
        } else {
          this.changeFormStatus(formId, true);
        }
        break;

      case QESTFormTemplateTypes.QEST_TPM_FORM:
        const selectedTPMFromItem = this.formsListingData.find(item => item.qestFormId === formId);
        if (event) {
          if (this.totalActivatedReports !== 0) {
            const isStatusChangeDiscard = await this.openConfirmationModal('Tracker Preventative Maintenance');
            if (isStatusChangeDiscard) {
              this.changeFormStatus(formId, true);
            } else {
              selectedTPMFromItem.isActive = !event;
            }
          } else {
            this.changeFormStatus(formId, true);
          }
        } else {
          this.changeFormStatus(formId, true);
        }
        break;

      default:
        break;
    }
  }

  changeFormStatus(formId: number, isGetAllApiCall: boolean = false) {
    this.loading = true;

    this.customFormService.changeFormStatus(formId).subscribe({
      next: response => {
        this.alertService.showSuccessToast(response.message);
        this.loading = false;
        if (isGetAllApiCall) {
          if (
            this.currrentTemplateType === QESTFormTemplateTypes.QEST_SUMMARY_REPORT ||
            this.currrentTemplateType === QESTFormTemplateTypes.QEST_COVER_PAGE ||
            this.currrentTemplateType === QESTFormTemplateTypes.QEST_TPM_FORM
          ) {
            this.getFormsList();
          }
        }
      },
      error: () => {
        const index = this.formsListingData.findIndex(form => form.qestFormId === formId);

        this.formsListingData[index].isActive = !this.formsListingData[index].isActive;
        this.loading = false;
      }
    });
  }

  getSelectedTemplateDetails(event) {
    this.isCoverPageTemplate = event.templateType === 3;
  }

  onChangeSize() {
    this.currentPage = 0;
    this.filterModel.page = 0;
    this.filterModel.itemsCount = Number(this.pageSize);
    this.getFormsList();
  }

  onPageChange(obj) {
    this.currentPage = obj;
    this.filterModel.page = this.currentPage - 1;
    this.getFormsList();
  }

  refreshList(filterParams: CommonFilter) {
    this.currrentTemplateType = filterParams.templateTypeId;
    this.formsListingData = [];
    this.totalCount = 0;
    this.totalActivatedReports = 0;
    this.currentPage = 1;
    this.getFormsList(true, filterParams);
  }

  goToLandingPage() {
    this.router.navigateByUrl('/entities/operations/custom-forms');
  }

  openModal(template: TemplateRef<any>) {
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      class: 'modal-md'
    };

    this.modalRef = this.modalService.show(template, ngModalOptions);
    this.getTemplateTypeDropdown();
  }

  onCancel() {
    this.modalRef.hide();
    this.customFormService.formName = '';
    this.customFormService.formNote = '';
    this.customFormService.selectedTemplateId = null;
  }

  createForm(form: NgForm) {
    if (form.valid) {
      this.router.navigateByUrl('/entities/operations/custom-forms/add-form');
      this.modalRef.hide();
    } else {
      form.control.markAllAsTouched();
    }
  }

  onFormDelete(event: any) {
    if (event) {
      const ngModalOptions: ModalOptions = {
        backdrop: 'static',
        keyboard: false,
        animated: true,
        initialState: { message: 'Are you sure want to delete this form?' }
      };

      this.modalRef = this.modalService.show(ConfirmDialogComponent, ngModalOptions);

      this.modalRef.content.onClose.subscribe({
        next: response => {
          if (response) {
            this.subscription.add(
              this.customFormService.deleteForm(event).subscribe({
                next: res => {
                  if (res) {
                    if (this.currentPage !== 0 && this.formsListingData.length === 1) {
                      this.onChangeSize();
                    } else {
                      this.getFormsList();
                    }
                    this.alertService.showSuccessToast(res.message);
                  }
                },
                error: () => (this.loading = false)
              })
            );
          }
        },
        error: () => (this.loading = false)
      });
    }
  }

  goToEditFormPage(formId: number) {
    this.router.navigateByUrl(`/entities/operations/custom-forms/edit-form/${formId}`);
  }

  cloneQESTForm(id: number) {
    this.loading = true;

    this.customFormService.cloneQESTForm(id).subscribe({
      next: res => {
        this.alertService.showSuccessToast(res.message);
        this.getFormsList();
      },
      error: () => (this.loading = false)
    });
  }

  getTemplateTypeDropdown() {
    this.loading = true;
    this.customFormService.getTemplateTypeDropdown(false).subscribe({
      next: (data: any) => {
        this.templateTypesList = data;
        this.loading = false;
      },
      error: () => (this.loading = false)
    });
  }

  gotoTemplates() {
    this.router.navigateByUrl(`/entities/operations/custom-forms/templates`);
  }
}

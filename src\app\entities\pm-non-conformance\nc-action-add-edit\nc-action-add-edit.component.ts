import { Component, EventEmitter, Input, OnDestroy, OnInit, ViewEncapsulation } from '@angular/core';
import { BsModalRef } from 'ngx-bootstrap/modal';
import { Subject, Subscription } from 'rxjs';
import { AlertService } from '../../../@shared/services';
import { StorageService } from '../../../@shared/services/storage.service';
import { ADD_EDIT_COPY_MODE, PmIssueObservationListResponse } from '../pm-non-conformance.model';
import { PmNonConformanceService } from '../pm-non-conformance.service';

@Component({
  selector: 'sfl-nc-action-add-edit',
  templateUrl: './nc-action-add-edit.component.html',
  styleUrls: ['./nc-action-add-edit.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class NcActionAddEditComponent implements OnInit, OnDestroy {
  public onClose: Subject<boolean>;
  @Input() actionItem;
  @Input() actionMode;
  @Input() issueObservationList: PmIssueObservationListResponse[];
  loading = false;
  subscription: Subscription = new Subscription();
  public event: EventEmitter<boolean> = new EventEmitter();

  constructor(
    public _bsModalRef: BsModalRef,
    private readonly storageService: StorageService,
    private readonly alertService: AlertService,
    private readonly pmNonConformanceService: PmNonConformanceService
  ) {}

  public ngOnInit(): void {
    this.onClose = new Subject();
    if (this.actionMode === ADD_EDIT_COPY_MODE.COPY) {
      this.actionItem = { ...this.actionItem, id: 0, actionRecommendation: 'Copy of ' + this.actionItem.actionRecommendation };
    }
  }

  public onConfirm(): void {
    this.onClose.next(true);
    this._bsModalRef.hide();
  }

  public onCancel(): void {
    this.onClose.next(false);
    this._bsModalRef.hide();
  }

  onAddEditAction() {
    this.loading = true;
    this.subscription.add(
      this.pmNonConformanceService.addUpdateNcActionRecommendation(this.actionItem).subscribe({
        next: (res: any) => {
          this.alertService.showSuccessToast(res.message);
          this.loading = false;
          this.event.emit(true);
          this._bsModalRef.hide();
        },
        error: e => {
          this.loading = false;
        }
      })
    );
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}

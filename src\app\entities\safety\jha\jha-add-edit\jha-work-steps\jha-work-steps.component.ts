import { Component, Input, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { FormArray, FormBuilder, FormControl, FormGroup, FormGroupDirective } from '@angular/forms';
import { Subscription } from 'rxjs';
import { AlertService } from '../../../../../@shared/services';
import { HazardService } from '../../../settings/hazard/hazard.service';
import { LOTO, LOTOBarrier, LOTOItemEmitResponse, WorkStep } from '../../../settings/settings.model';
import { WorkTypeService } from '../../../settings/work-type/work-type.service';
import { JhaUploadService } from '../jha-upload.service';
import { HazardDetailByWorkType, WorkStepDetailByWorkType, WorkTypes } from '../../../../../@shared/models/jha.model';
import { CdkDragDrop, moveItemInArray } from '@angular/cdk/drag-drop';
import { JhaHelperService } from '../../jha-helper.service';
import { LotoAddEditComponent } from '../../../settings/loto/loto-add-edit/loto-add-edit.component';
import { BsModalRef, BsModalService, ModalOptions } from 'ngx-bootstrap/modal';

@Component({
  selector: 'sfl-jha-work-steps',
  templateUrl: './jha-work-steps.component.html',
  styleUrls: ['./jha-work-steps.component.scss']
})
export class JhaWorkStepsComponent implements OnInit, OnDestroy {
  @Input() workTypesForm: string;
  @Input() workStepsForm: string;
  @Input() hazardsForm: string;
  @Input() lotoBarrierForm: string;
  private subscription: Subscription = new Subscription();

  masterWorkStepListLoading = false;
  noWorkTypeSelected = false;
  masterWorkStepList = [];
  workTypes: FormArray;
  listOfWorkStep: FormArray;
  listOfHazard: FormArray;
  lotoBarrier: FormControl;
  modalRef: BsModalRef;

  constructor(
    private rootFormGroup: FormGroupDirective,
    private readonly modalService: BsModalService,
    private readonly workTypeService: WorkTypeService,
    private readonly jhaHelperService: JhaHelperService
  ) {}

  ngOnInit(): void {
    this.workTypes = this.rootFormGroup.control.get(this.workTypesForm) as FormArray;
    this.listOfWorkStep = this.rootFormGroup.control.get(this.workStepsForm) as FormArray;
    this.listOfHazard = this.rootFormGroup.control.get(this.hazardsForm) as FormArray;
    this.lotoBarrier = this.rootFormGroup.control.get(this.lotoBarrierForm) as FormControl;
    this.getAllActiveWorkStepsList();
    this.setOrderOfListOfWorkStepItems();
  }

  get workTypeName(): string {
    return this.workTypes.value.map((item: WorkTypes) => item.workTypeName).join(', ');
  }

  setOrderOfListOfWorkStepItems(): void {
    let i = 1;
    this.listOfWorkStep.controls.forEach((item: FormGroup, index: number) => {
      item.get('orderValue')?.setValue(i++);
      if (index === 0) {
        item.get('orderChange')?.setValue(true);
      }
    });
  }

  dropWorkSteps(event: CdkDragDrop<string[]>): void {
    moveItemInArray(this.listOfWorkStep.controls, event.previousIndex, event.currentIndex);
    this.setOrderOfListOfWorkStepItems();
  }

  deleteWorkStepItem(workStepItem: FormGroup, index: number): void {
    this.listOfWorkStep.removeAt(index);
    this.setOrderOfListOfWorkStepItems();
  }

  addWorkStepIntoList(workStepItem: WorkStepDetailByWorkType): void {
    const formGroup = this.jhaHelperService.initOrPatchListOfWorkSteps(workStepItem);
    this.listOfWorkStep.push(formGroup);
  }

  onLotoWorkStepChange(): void {
    const hasSelectedLotoWorkStep = this.listOfWorkStep.value.some(item => item.isLotoWorkStep && item.isIncludedWorkStep);

    this.listOfHazard.controls.forEach((item: FormGroup) => {
      if (item.get('isLotoHazard')?.value) {
        item.get('isIncludedHazard')?.setValue(hasSelectedLotoWorkStep);
      }
    });
  }

  addLotoIntoListOfWorkSteps(lotoWorkStepItem: LOTO, index: number, isEditMode: boolean): void {
    const workStepItem = new WorkStepDetailByWorkType(
      lotoWorkStepItem.id,
      lotoWorkStepItem.name,
      lotoWorkStepItem.equipmentType,
      lotoWorkStepItem.equipmentModel,
      lotoWorkStepItem.asBuildPageNumber,
      true,
      0,
      true
    );

    const workStepFormGroup = this.jhaHelperService.initOrPatchListOfWorkSteps(workStepItem);
    if (isEditMode) {
      this.listOfWorkStep.at(index).patchValue(workStepFormGroup.value);
    } else {
      this.listOfWorkStep.insert(index, workStepFormGroup);
    }
    this.onLotoWorkStepChange();
  }

  addLotoIntoListOfHazards(lotoHazardItem: LOTOBarrier, index: number, isEditMode: boolean): void {
    if (lotoHazardItem && lotoHazardItem.riskLevel) {
      const hazardItem = new HazardDetailByWorkType(
        lotoHazardItem.id,
        lotoHazardItem.name,
        lotoHazardItem.riskLevelStr,
        lotoHazardItem.riskLevel,
        lotoHazardItem.controlBarrier,
        lotoHazardItem.protectiveBarrier,
        lotoHazardItem.supportBarrier,
        true,
        0,
        true
      );

      const hazardFormGroup = this.jhaHelperService.initOrPatchListOfHazards(hazardItem);
      if (isEditMode) {
        this.listOfHazard.at(index).patchValue(hazardFormGroup.value);
      } else {
        this.listOfHazard.insert(index, hazardFormGroup);
      }
    }
  }

  setWorkStepsList(): void {
    const availableWorkStepsInList = this.listOfWorkStep.value.map((item: WorkStepDetailByWorkType) => item.workStepId);
    this.masterWorkStepList = this.masterWorkStepList.map(item => ({
      ...item,
      disabled: availableWorkStepsInList.includes(item.workStepId)
    }));
  }

  setLotoBarrierForAction(lotoHazard: LOTOBarrier | HazardDetailByWorkType): LOTOBarrier | null {
    if (!lotoHazard) return null;

    const isHazardDetail = 'hazardId' in lotoHazard || 'hazards' in lotoHazard;

    return new LOTOBarrier({
      id: isHazardDetail ? lotoHazard.hazardId : lotoHazard.id,
      name: isHazardDetail ? lotoHazard.hazards : lotoHazard.name,
      riskLevel: isHazardDetail ? lotoHazard.riskLevelId : lotoHazard.riskLevel,
      controlBarrier: isHazardDetail ? lotoHazard.controlBarriers : lotoHazard.controlBarrier,
      supportBarrier: isHazardDetail ? lotoHazard.supportBarriers : lotoHazard.supportBarrier,
      protectiveBarrier: isHazardDetail ? lotoHazard.protectiveBarriers : lotoHazard.protectiveBarrier,
      riskLevelStr: isHazardDetail ? lotoHazard.riskLevel : lotoHazard.riskLevelStr
    });
  }

  addEditLotoWorkStep(workStepItem: FormGroup, index: number, isEditMode = false): void {
    const lotoBarrier = this.listOfHazard?.value?.find(item => item.isLotoHazard) ?? null;
    const lotoHazard = isEditMode ? lotoBarrier : this.lotoBarrier?.value ?? null;

    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      id: index,
      class: 'modal-md',
      initialState: {
        fromJhaWorkStep: true,
        lotoItem: workStepItem?.value || null,
        lotoBarrier: this.setLotoBarrierForAction(lotoHazard),
        lotoList:
          this.listOfWorkStep?.value
            .filter(item => item.isLotoWorkStep)
            .map(item => new LOTO(item.workStepId, item.workStep, item.equipmentType, item.equipmentModel, item.asBuildPageNumber)) || []
      }
    };
    this.modalRef = this.modalService.show(LotoAddEditComponent, ngModalOptions);

    this.subscription.add(
      this.modalRef.content.onLotoItemEmit.subscribe((lotoEmitResponse: LOTOItemEmitResponse) => {
        if (lotoEmitResponse) {
          this.addLotoIntoListOfWorkSteps(lotoEmitResponse.lotoWorkStepItem, index, isEditMode);
          if (!lotoBarrier) {
            this.addLotoIntoListOfHazards(lotoEmitResponse.lotoHazardItem, index, isEditMode);
          }
        }
      })
    );
  }

  getAllActiveWorkStepsList(isActive = true): void {
    const workTypes = this.workTypes.value;
    const hasWorkTypeIds = workTypes.filter(x => x.workTypeId).length > 0 ? true : false;

    if (hasWorkTypeIds) {
      this.masterWorkStepListLoading = true;
      this.subscription.add(
        this.workTypeService.getAllActiveWorkStepsList(isActive).subscribe({
          next: (res: any) => {
            this.masterWorkStepList = res.map((item: WorkStep) => new WorkStepDetailByWorkType(item.id, item.workStepName));
            this.masterWorkStepListLoading = false;
          },
          error: _e => {
            this.masterWorkStepListLoading = false;
          }
        })
      );
    } else {
      this.noWorkTypeSelected = true;
    }
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }
}

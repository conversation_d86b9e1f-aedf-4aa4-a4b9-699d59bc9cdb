<nb-card class="siteSpinner appSpinner" [nbSpinner]="loading" nbSpinnerStatus="primary" nbSpinnerSize="large">
  <nb-card-header>
    <div class="row">
      <div class="col-12 d-flex align-items-center">
        <h6>Reschedule Breakdown - Pivot Table and Bar Chart</h6>
        <div class="ms-auto">
          <button
            class="linear-mode-button"
            nbButton
            status="primary"
            size="small"
            type="button"
            (click)="exportData()"
            [disabled]="!isViewChart"
          >
            <span class="d-none d-lg-inline-block">Export</span>
            <i class="d-inline-block d-lg-none fa fa-file-export"></i>
          </button>
        </div>
      </div>
    </div>
  </nb-card-header>
  <nb-card-body class="dropdownOverlap">
    <div class="row">
      <div class="col-12 report-Filter">
        <div class="form-control-group mb-3">
          <div class="row align-items-center">
            <div class="col-12 col-sm-6 col-md-4 col-lg-3 mb-2 pe-lg-0">
              <label class="label" for="customer">Customer</label>
              <ng-select
                id="reports-customer-drop-down"
                class="sfl-track-dropdown"
                name="Customer"
                [items]="customerList"
                [multiple]="true"
                (change)="onCustomerSelectDeSelect(); filterModel.primaryMetric = null"
                (clear)="clearSingleFilter('customer')"
                bindLabel="name"
                bindValue="id"
                [(ngModel)]="filterModel.customerIds"
                notFoundText="No Customer Found"
                placeholder="Select Customer"
                [closeOnSelect]="false"
                appendTo="body"
                [loading]="customerLoading"
                (search)="onFilter($event, 'filteredCustomersIds')"
                (close)="filteredCustomersIds = []"
              >
                <ng-template ng-header-tmp *ngIf="customerList && customerList.length">
                  <button type="button" (click)="selectUnselectAllCustomers(true)" class="btn btn-sm btn-primary me-2">Select all</button>
                  <button type="button" (click)="selectUnselectAllCustomers(false)" class="btn btn-sm btn-primary ml-2">
                    Unselect all
                  </button>
                </ng-template>
                <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                  <input id="item-{{ index }}" type="checkbox" [ngModel]="item$.selected" /> {{ item.name }}
                </ng-template>
                <ng-template ng-multi-label-tmp let-items="items" let-clear="clear">
                  <div class="ng-value d-flex" *ngFor="let item of items | slice : 0 : 1">
                    <span
                      class="ng-value-label text-truncate"
                      [ngClass]="{
                        'w-px-48': (null | screenSize) < 1400 && items?.length > 1,
                        'w-px-75': (null | screenSize) < 1400 && assesmentDto?.name?.length > 4 && items?.length === 1,
                        'w-px-110': (null | screenSize) > 1400 && items?.length >= 1
                      }"
                      >{{ item.name }}</span
                    >
                    <span class="ng-value-icon right" (click)="clear(item)" aria-hidden="true">×</span>
                  </div>
                  <div class="ng-value" *ngIf="items.length > 1">
                    <span class="ng-value-label">+{{ items.length - 1 }} </span>
                  </div>
                </ng-template>
              </ng-select>
            </div>
            <div class="col-12 col-sm-6 col-md-4 col-lg-3 mb-2 pe-lg-0">
              <label class="label" for="portfolio">Portfolio</label>
              <ng-select
                id="reports-portfolio-drop-down"
                class="sfl-track-dropdown"
                name="Portfolio"
                [multiple]="true"
                [items]="portfolioList"
                (change)="onPortfolioSelectDeSelect(); filterModel.primaryMetric = null"
                (clear)="clearSingleFilter('portfolio')"
                bindLabel="name"
                bindValue="id"
                [(ngModel)]="filterModel.portfolioIds"
                [closeOnSelect]="false"
                notFoundText="No Portfolio Found"
                placeholder="Select Portfolio"
                appendTo="body"
                [loading]="portfolioLoading"
                (search)="onFilter($event, 'filteredPortfolioIds')"
                (close)="filteredPortfolioIds = []"
              >
                <ng-template ng-header-tmp *ngIf="portfolioList && portfolioList.length">
                  <button type="button" (click)="selectUnselectAllPortfolio(true)" class="btn btn-sm btn-primary me-2">Select all</button>
                  <button type="button" (click)="selectUnselectAllPortfolio(false)" class="btn btn-sm btn-primary ml-2">
                    Unselect all
                  </button>
                </ng-template>
                <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                  <input id="item-{{ index }}" type="checkbox" [ngModel]="item$.selected" /> {{ item.name }}
                </ng-template>
                <ng-template ng-multi-label-tmp let-items="items" let-clear="clear">
                  <div class="ng-value d-flex" *ngFor="let item of items | slice : 0 : 1">
                    <span
                      class="ng-value-label text-truncate"
                      [ngClass]="{
                        'w-px-48': (null | screenSize) < 1400 && items?.length > 1,
                        'w-px-75': (null | screenSize) < 1400 && item?.name?.length > 5 && items?.length === 1,
                        'w-px-110': (null | screenSize) > 1400 && items?.length >= 1
                      }"
                    >
                      {{ item.name }}</span
                    >
                    <span class="ng-value-icon right" (click)="clear(item)" aria-hidden="true">×</span>
                  </div>
                  <div class="ng-value h-28px" *ngIf="items.length > 1">
                    <span class="ng-value-label">+{{ items.length - 1 }} </span>
                  </div>
                </ng-template>
              </ng-select>
            </div>
            <div class="col-12 col-sm-6 col-md-4 col-lg-3 mb-2 pe-lg-0">
              <label class="label" for="input-siteId">Site</label>
              <ng-select
                id="reports-site-drop-down"
                class="sfl-track-dropdown"
                name="Site"
                [multiple]="true"
                [items]="siteList"
                (clear)="clearSingleFilter('site')"
                (change)="filterModel.primaryMetric = null"
                bindLabel="name"
                bindValue="id"
                [(ngModel)]="filterModel.siteIds"
                [closeOnSelect]="false"
                notFoundText="No Site Found"
                placeholder="Select Site"
                appendTo="body"
                [loading]="siteLoading"
                (search)="onFilter($event, 'filteredSiteIds')"
                (close)="filteredSiteIds = []"
              >
                <ng-template ng-header-tmp *ngIf="siteList && siteList.length">
                  <button type="button" (click)="selectUnselectAllSite(true)" class="btn btn-sm btn-primary me-2">Select all</button>
                  <button type="button" (click)="selectUnselectAllSite(false)" class="btn btn-sm btn-primary ml-2">Unselect all</button>
                </ng-template>
                <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                  <input id="item-{{ index }}" type="checkbox" [ngModel]="item$.selected" /> {{ item.name }}
                </ng-template>
                <ng-template ng-multi-label-tmp let-items="items" let-clear="clear">
                  <div class="ng-value d-flex" *ngFor="let item of items | slice : 0 : 1">
                    <span
                      class="ng-value-label text-truncate"
                      [ngClass]="{
                        'w-px-48': (null | screenSize) < 1400 && items?.length > 1,
                        'w-px-75': (null | screenSize) < 1400 && item?.name?.length > 5 && items?.length === 1,
                        'w-px-110': (null | screenSize) > 1400 && items?.length >= 1
                      }"
                      >{{ item.name }}</span
                    >
                    <span class="ng-value-icon right" (click)="clear(item)" aria-hidden="true">×</span>
                  </div>
                  <div class="ng-value" *ngIf="items.length > 1">
                    <span class="ng-value-label">+{{ items.length - 1 }} </span>
                  </div>
                </ng-template>
              </ng-select>
            </div>
            <div class="col-12 col-sm-6 col-md-4 col-lg-3 mb-2 pe-lg-0">
              <label class="label" for="input-workorderId">Assessment Type</label>
              <ng-select
                id="assessment-type-drop-down"
                class="sfl-track-dropdown"
                name="Report Type"
                [multiple]="true"
                [items]="reportTypeData"
                (change)="onFilterChange()"
                (clear)="clearSingleFilter('Assessment Type')"
                bindLabel="name"
                bindValue="abbreviation"
                [closeOnSelect]="false"
                [(ngModel)]="filterModel.assessmentTypes"
                notFoundText="No Assessment Type Found"
                placeholder="Select Assessment Type"
                appendTo="body"
              >
                <ng-template ng-header-tmp *ngIf="reportTypeData && reportTypeData.length">
                  <button
                    (click)="selectAndDeselectAll(reportTypeData, 'assessmentTypes', true, 'abbreviation')"
                    class="btn btn-sm btn-primary sfl-select-all-btn"
                  >
                    Select all
                  </button>
                  <button (click)="clearSingleFilter('Assessment Type')" class="btn btn-sm btn-primary ms-1 sfl-unselect-all-btn">
                    Unselect all
                  </button>
                </ng-template>
                <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                  <input id="item-{{ index }}" type="checkbox" [ngModel]="item$.selected" /> {{ item.name }}
                </ng-template>
                <ng-template ng-multi-label-tmp let-items="items" let-clear="clear">
                  <div class="ng-value d-flex" *ngFor="let item of items | slice : 0 : 1">
                    <span
                      class="ng-value-label text-truncate"
                      [ngClass]="{
                        'w-px-48': (null | screenSize) < 1400 && items?.length > 1,
                        'w-px-75': (null | screenSize) < 1400 && item?.name?.length > 5 && items?.length === 1,
                        'w-px-110': (null | screenSize) > 1400 && items?.length >= 1
                      }"
                      >{{ item.name }}</span
                    >
                    <span class="ng-value-icon right" (click)="clear(item)" aria-hidden="true">×</span>
                  </div>
                  <div class="ng-value" *ngIf="items.length > 1">
                    <span class="ng-value-label">+{{ items.length - 1 }} </span>
                  </div>
                </ng-template>
              </ng-select>
            </div>
            <!-- Region Multi select-->
            <div class="col-12 col-sm-6 col-md-4 col-lg-3 mb-2 pe-lg-0">
              <label class="label" for="input-siteId">Region</label>
              <ng-select
                id="region-drop-down"
                class="sfl-track-dropdown"
                name="Region"
                [multiple]="true"
                [items]="regionList"
                (clear)="clearSingleFilter('regionIds')"
                (change)="onFilterChange()"
                bindLabel="name"
                bindValue="id"
                [(ngModel)]="filterModel.regionIds"
                [closeOnSelect]="false"
                notFoundText="No Region Found"
                placeholder="Select Region"
                appendTo="body"
                (search)="onFilter($event, 'filteredRegionIds')"
                (close)="filteredRegionIds = []"
              >
                <ng-template ng-header-tmp *ngIf="regionList && regionList.length">
                  <button type="button" (click)="selectUnselectAllRegion(true)" class="btn btn-sm btn-primary me-2">Select all</button>
                  <button type="button" (click)="selectUnselectAllRegion(false)" class="btn btn-sm btn-primary ml-2">Unselect all</button>
                </ng-template>
                <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                  <input id="item-{{ index }}" type="checkbox" [ngModel]="item$.selected" /> {{ item.name }}
                </ng-template>
                <ng-template ng-multi-label-tmp let-items="items" let-clear="clear">
                  <div class="ng-value d-flex" *ngFor="let item of items | slice : 0 : 1">
                    <span
                      class="ng-value-label text-truncate"
                      [ngClass]="{
                        'w-px-48': (null | screenSize) < 1400 && items?.length > 1,
                        'w-px-75': (null | screenSize) < 1400 && item?.name?.length > 5 && items?.length === 1,
                        'w-px-110': (null | screenSize) > 1400 && items?.length >= 1
                      }"
                      >{{ item.name }}</span
                    >
                    <span class="ng-value-icon right" (click)="clear(item)" aria-hidden="true">×</span>
                  </div>
                  <div class="ng-value" *ngIf="items.length > 1">
                    <span class="ng-value-label">+{{ items.length - 1 }} </span>
                  </div>
                </ng-template>
              </ng-select>
            </div>

            <!-- Subregion Multi select-->
            <div class="col-12 col-sm-6 col-md-4 col-lg-3 mb-2 pe-lg-0">
              <label class="label" for="input-siteId">Subregion</label>
              <ng-select
                id="region-drop-down"
                class="sfl-track-dropdown"
                name="Subregion"
                [multiple]="true"
                [items]="subRegionList"
                (clear)="clearSingleFilter('subregionIds')"
                (change)="onFilterChange()"
                bindLabel="name"
                bindValue="id"
                [(ngModel)]="filterModel.subregionIds"
                [closeOnSelect]="false"
                notFoundText="No Subregion Found"
                placeholder="Select Subregion"
                appendTo="body"
                (search)="onFilter($event, 'filteredSubregionIds')"
                (close)="filteredSubregionIds = []"
              >
                <ng-template ng-header-tmp *ngIf="regionList && regionList.length">
                  <button type="button" (click)="selectUnselectAllSubRegion(true)" class="btn btn-sm btn-primary me-2">Select all</button>
                  <button type="button" (click)="selectUnselectAllSubRegion(false)" class="btn btn-sm btn-primary ml-2">
                    Unselect all
                  </button>
                </ng-template>
                <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                  <input id="item-{{ index }}" type="checkbox" [ngModel]="item$.selected" /> {{ item.name }}
                </ng-template>
                <ng-template ng-multi-label-tmp let-items="items" let-clear="clear">
                  <div class="ng-value d-flex" *ngFor="let item of items | slice : 0 : 1">
                    <span
                      class="ng-value-label text-truncate"
                      [ngClass]="{
                        'w-px-48': (null | screenSize) < 1400 && items?.length > 1,
                        'w-px-75': (null | screenSize) < 1400 && item?.name?.length > 5 && items?.length === 1,
                        'w-px-110': (null | screenSize) > 1400 && items?.length >= 1
                      }"
                      >{{ item.name }}</span
                    >
                    <span class="ng-value-icon right" (click)="clear(item)" aria-hidden="true">×</span>
                  </div>
                  <div class="ng-value" *ngIf="items.length > 1">
                    <span class="ng-value-label">+{{ items.length - 1 }} </span>
                  </div>
                </ng-template>
              </ng-select>
            </div>
            <div class="col-12 col-sm-6 col-md-4 col-lg-3 mb-2 pe-lg-0">
              <label class="label" for="year"
                >Row Metric <span *ngIf="!filterModel.secondaryMetric" class="ms-1 text-danger">*</span></label
              >
              <ng-select
                class="sfl-track-dropdown"
                id="primaryMetric"
                name="primaryMetric"
                [(ngModel)]="filterModel.primaryMetric"
                placeholder="Select Row Metric"
                notFoundText="No Row Metric Found"
                [closeOnSelect]="true"
                appendTo="body"
              >
                <ng-option value="2">Customer</ng-option>
                <ng-option value="3">Portfolio</ng-option>
                <ng-option value="4">Site</ng-option>
                <ng-option value="10">Region</ng-option>
                <ng-option value="11">Subregion</ng-option>
                <ng-option value="5">CAM</ng-option>
                <ng-option value="6">PL</ng-option>
                <ng-option value="7">Reschedule Main Category</ng-option>
                <ng-option value="8">Tech Assigned</ng-option>
                <ng-option value="9">Assessment Type</ng-option>
              </ng-select>
            </div>
            <div class="col-12 col-sm-6 col-md-4 col-lg-3 mb-2 pe-lg-0">
              <label class="label" for="year"
                >Column Metric <span *ngIf="!filterModel.primaryMetric" class="ms-1 text-danger">*</span></label
              >
              <ng-select
                class="sfl-track-dropdown"
                id="secondaryMetric"
                name="secondaryMetric"
                [items]="secondaryMetricList"
                bindLabel="name"
                bindValue="id"
                [(ngModel)]="filterModel.secondaryMetric"
                notFoundText="No Column Metric Found"
                placeholder="Select Column Metric"
                [closeOnSelect]="true"
                appendTo="body"
              >
              </ng-select>
            </div>
            <div class="col-12 col-sm-6 col-md-4 col-lg-3 mb-2 pe-lg-0">
              <label class="label" for="year">Quarter</label>
              <ng-select
                class="sfl-track-dropdown"
                id="Quarter"
                name="Quarter"
                [multiple]="true"
                [items]="quarterList"
                bindLabel="name"
                bindValue="name"
                (ngModelChange)="getMonthsBasedOnQuarter($event)"
                (change)="onFilterChange()"
                [(ngModel)]="filterModel.quarter"
                notFoundText="No Quarter Found"
                placeholder="Select Quarter"
                [closeOnSelect]="false"
                appendTo="body"
              >
                <ng-template ng-header-tmp *ngIf="quarterList && quarterList.length">
                  <button
                    (click)="selectAndDeselectAll(quarterList, 'quarter', true, 'name')"
                    class="btn btn-sm btn-primary sfl-select-all-btn"
                  >
                    Select all
                  </button>
                  <button (click)="clearSingleFilter('quarter')" class="btn btn-sm btn-primary ms-1 sfl-unselect-all-btn">
                    Unselect all
                  </button>
                </ng-template>
                <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                  <input id="item-{{ index }}" type="checkbox" [ngModel]="item$.selected" /> {{ item.name }}
                </ng-template>
                <ng-template ng-multi-label-tmp let-items="items" let-clear="clear">
                  <div class="ng-value d-flex" *ngFor="let item of items | slice : 0 : 1">
                    <span
                      class="ng-value-label text-truncate"
                      [ngClass]="{
                        'w-px-48': (null | screenSize) < 1400 && items?.length > 1,
                        'w-px-75': (null | screenSize) < 1400 && item?.name?.length > 5 && items?.length === 1,
                        'w-px-110': (null | screenSize) > 1400 && items?.length >= 1
                      }"
                      >{{ item.name }}</span
                    >
                    <span class="ng-value-icon right" (click)="clear(item)" aria-hidden="true">×</span>
                  </div>
                  <div class="ng-value" *ngIf="items.length > 1">
                    <span class="ng-value-label">+{{ items.length - 1 }} </span>
                  </div>
                </ng-template>
              </ng-select>
            </div>
            <div class="col-12 col-sm-6 col-md-4 col-lg-3 mb-2 pe-lg-0">
              <div class="row">
                <div class="col-sm-12 col-md-7 custom-select-value">
                  <label class="label" for="year">Month</label>
                  <ng-select
                    class="sfl-track-dropdown"
                    id="Month"
                    name="Month"
                    [multiple]="true"
                    [items]="monthsList"
                    bindLabel="name"
                    bindValue="id"
                    [(ngModel)]="filterModel.month"
                    (change)="onFilterChange()"
                    notFoundText="No Month Found"
                    placeholder="Select Month"
                    [closeOnSelect]="false"
                    appendTo="body"
                  >
                    <ng-template ng-header-tmp *ngIf="monthsList && monthsList.length">
                      <button (click)="selectAndDeselectAll(monthsList, 'month', true)" class="btn btn-sm btn-primary sfl-select-all-btn">
                        Select all
                      </button>
                      <button (click)="clearSingleFilter('month')" class="btn btn-sm btn-primary ms-1 sfl-unselect-all-btn">
                        Unselect all
                      </button>
                    </ng-template>
                    <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                      <input id="item-{{ index }}" type="checkbox" [ngModel]="item$.selected" /> {{ item.name }}
                    </ng-template>
                    <ng-template ng-multi-label-tmp let-items="items" let-clear="clear">
                      <div class="ng-value d-flex" *ngFor="let item of items | slice : 0 : 1">
                        <span
                          class="ng-value-label text-truncate"
                          [ngClass]="{
                            'w-px-48': (null | screenSize) < 1400 && items?.length > 1,
                            'w-px-75': (null | screenSize) < 1400 && item?.name?.length > 5 && items?.length === 1,
                            'w-px-110': (null | screenSize) > 1400 && items?.length >= 1
                          }"
                          >{{ item.name }}</span
                        >
                        <span class="ng-value-icon right" (click)="clear(item)" aria-hidden="true">×</span>
                      </div>
                      <div class="ng-value" *ngIf="items.length > 1">
                        <span class="ng-value-label">+{{ items.length - 1 }} </span>
                      </div>
                    </ng-template>
                  </ng-select>
                </div>
                <div class="col-sm-12 col-md-5">
                  <label class="label" for="year">Year</label>
                  <ng-select
                    class="sfl-track-dropdown"
                    id="startYear"
                    name="startYear"
                    [items]="years"
                    bindLabel="name"
                    bindValue="id"
                    [(ngModel)]="filterModel.year"
                    (change)="onFilterChange()"
                    (clear)="clearSingleFilter('year')"
                    notFoundText="No Year Found"
                    placeholder="Select Year"
                    [closeOnSelect]="true"
                    [clearable]="false"
                    appendTo="body"
                  >
                  </ng-select>
                </div>
              </div>
            </div>
            <div class="col-12 col-sm-6 col-md-4 col-lg-3 mt-2 mt-sm-4 pe-lg-0">
              <div class="row">
                <div class="col-7 col-xxl-5 pe-1">
                  <button
                    nbButton
                    id="performance-view-data-btn"
                    class="w-100"
                    status="primary"
                    size="small"
                    type="button"
                    [disabled]="!filterModel.primaryMetric && !filterModel.secondaryMetric"
                    (click)="viewData()"
                  >
                    View Data
                  </button>
                </div>
                <div class="col-5 col-xxl-4 ps-1">
                  <button
                    nbButton
                    id="performance-clear-filter-btn"
                    class="w-100"
                    status="primary"
                    size="small"
                    type="button"
                    [disabled]="loading"
                    (click)="clearFilters()"
                  >
                    clear
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div *ngIf="isViewChart">
      <div class="categoryBy chart-box" *ngIf="byCategoryChartData.eChartObjects?.length">
        <sfl-reschedule-category-chart
          [byCategoryChartConfig]="byCategoryChartData"
          [chartLabel]="chartLabel"
          [RescheduleBy]="byCategoryChartData.chartHeader"
        >
        </sfl-reschedule-category-chart>
      </div>
      <div class="category-table mt-3 chart-box" *ngIf="pivotTableData.columnsData?.length">
        <nb-card class="dataSourceSpinner appSpinner" [nbSpinner]="loading" nbSpinnerStatus="primary" nbSpinnerSize="large">
          <nb-card-header>
            <div class="row">
              <div class="col-12 d-flex align-items-center">
                <h6>{{ chartLabel }}</h6>
              </div>
            </div>
          </nb-card-header>
          <nb-card-body>
            <div class="row">
              <div id="fixed-table" setTableHeight class="col-12 table-responsive">
                <table class="table table-hover table-bordered" aria-describedby="data source">
                  <thead>
                    <tr>
                      <th *ngFor="let headers of pivotTableData.headers">{{ headers }}</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr *ngFor="let column of pivotTableData.columnsData">
                      <td *ngFor="let data of column.data">{{ data }}</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </nb-card-body>
        </nb-card>
      </div>
    </div>
  </nb-card-body>
</nb-card>

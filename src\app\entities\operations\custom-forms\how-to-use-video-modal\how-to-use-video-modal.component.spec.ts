import { ComponentFixture, TestBed } from '@angular/core/testing';

import { HowToUseVideoModalComponent } from './how-to-use-video-modal.component';

describe('HowToUseVideoModalComponent', () => {
  let component: HowToUseVideoModalComponent;
  let fixture: ComponentFixture<HowToUseVideoModalComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ HowToUseVideoModalComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(HowToUseVideoModalComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});

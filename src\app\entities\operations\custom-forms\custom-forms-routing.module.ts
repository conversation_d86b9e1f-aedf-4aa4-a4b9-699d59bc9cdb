import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { AddEditTemplateComponent } from './add-edit-template/add-edit-template.component';
import { CustomFormsLandingPageComponent } from './custom-forms-landing-page/custom-forms-landing-page.component';
import { FormAnalyticsComponent } from './form-analytics/form-analytics.component';
import { MyFormsListingPageComponent } from './my-forms-listing-page/my-forms-listing-page.component';
import { TemplateListingComponent } from './template-listing/template-listing.component';

const routes: Routes = [
  {
    path: '',
    component: CustomFormsLandingPageComponent,
    data: { permittedRoles: ['admin', 'commercialAssetsManager', 'fieldTech'], pageTitle: 'Custom Forms' }
  },
  {
    path: 'templates',
    component: TemplateListingComponent,
    data: { permittedRoles: ['admin', 'commercialAssetsManager', 'fieldTech'], pageTitle: 'QEST Templates' }
  },
  {
    path: 'add-template',
    component: AddEditTemplateComponent,
    data: {
      permittedRoles: ['admin', 'commercialAssetsManager', 'fieldTech'],
      pageTitle: 'Add QEST Template'
    }
  },
  {
    path: 'edit-template/:id',
    component: AddEditTemplateComponent,
    data: {
      permittedRoles: ['admin', 'commercialAssetsManager', 'fieldTech'],
      pageTitle: 'Edit QEST Templates'
    }
  },
  {
    path: 'forms',
    component: MyFormsListingPageComponent,
    data: { permittedRoles: ['admin', 'commercialAssetsManager', 'fieldTech'], pageTitle: 'QEST Forms' }
  },
  {
    path: 'add-form',
    component: AddEditTemplateComponent,
    data: { permittedRoles: ['admin', 'commercialAssetsManager', 'fieldTech'], pageTitle: 'Add QEST Form' }
  },
  {
    path: 'edit-form/:id',
    component: AddEditTemplateComponent,
    data: { permittedRoles: ['admin', 'commercialAssetsManager', 'fieldTech'], pageTitle: 'Edit QEST Form' }
  },
  {
    path: 'fill-form/:id',
    component: AddEditTemplateComponent
    // data: { permittedRoles: ['admin', 'commercialAssetsManager', 'fieldTech', 'portfolioManager', 'analyst'], pageTitle: 'QEST Form' }
  },
  {
    path: 'fill-mobile-form',
    component: AddEditTemplateComponent
  },
  {
    path: 'form-analytics',
    component: FormAnalyticsComponent,
    data: {
      permittedRoles: ['admin', 'commercialAssetsManager', 'fieldTech'],
      pageTitle: 'QEST Form Analytics'
    }
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class CustomFormsRoutingModule {}

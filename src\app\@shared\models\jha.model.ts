import { LOTO } from '../../entities/safety/settings/settings.model';
import { Dropdown } from './dropdown.model';
import * as uuid from 'uuid';

export class Barrier {
  public barrierCount: number;
  public barrierList: BarrierDetail[];
}

export class BarrierDetail {
  public barrierType: number;
  public barrierTypeStr: string;
  public description: string;
  public id: number;
}

export class HazardDetail {
  public id: number;
  public name: string;
  public riskLevel: number;
  public riskLevelStr: string;
  public barriers: BarrierArrayList[];
}

export class BarrierArrayList {
  public barrierId: number;
  public barrierType?: number;
  public barrierTypeStr?: string;
  public description?: string;
  public id?: number;
  public isDeleted?: boolean;
  public isUpdated?: boolean;
}

export class WorkStepDetail {
  public id;
  public workSteps: StepDetail[];
  public workTypeId: number;
  public workTypeName: string;
}

export class StepDetail {
  public controlBarriers: string;
  public hazards: string;
  public protectiveBarriers: string;
  public reportWorkStepId: number;
  public riskLevel: string;
  public supportBarriers: string;
  public workStepName: string;
  public rowCount: number;
  public orderValue: number;
}
export class WorkTypeList {
  public result: Dropdown[];
  public id: number;
  public exception: any;
  public status: number;
  public isCanceled: boolean;
  public isCompleted: boolean;
  public isCompletedSuccessfully: boolean;
  public creationOptions: number;
  public asyncState: any;
  public isFaulte: boolean;
}

export const hazardsRiskLevelList = [
  { name: 'High', nameStr: 'H', id: 1 },
  { name: 'Medium', nameStr: 'M', id: 2 },
  { name: 'Low', nameStr: 'L', id: 3 }
];

export class WorkTypes {
  workTypeId: number;
  workTypeName: string;
  reportId: string;
  id: string;
  constructor(workTypeId: number, workTypeName: string, reportId: string, id: string) {
    this.workTypeId = workTypeId;
    this.workTypeName = workTypeName;
    this.reportId = reportId;
    this.id = id;
  }
}

export class WorkSteps {
  workStepId: number;
  workStep: string;
  isLotoWorkStep: boolean;
  reportId: string;
  id: string;
  constructor(workStepId: number, workStep: string, isLotoWorkStep: boolean, reportId: string, id: string) {
    this.workStepId = workStepId;
    this.workStep = workStep;
    this.isLotoWorkStep = isLotoWorkStep;
    this.reportId = reportId;
    this.id = id;
  }
}

export class WorkTypesForWorkStep {
  workTypeId: number;
  workType: string;

  constructor(workTypeId: number, workType: string) {
    this.workTypeId = workTypeId;
    this.workType = workType;
  }
}

export class WorkStepsForHazards {
  workStepId: number;
  workStep: string;

  constructor(workStepId: number, workStep: string) {
    this.workStepId = workStepId;
    this.workStep = workStep;
  }
}

export interface WorkStepAndHazardListByWorkType {
  listOfWorkStep: WorkStepDetailByWorkType[];
  listOfHazard: HazardDetailByWorkType[];
}

export class HazardDetailByWorkType {
  public hazardId: number;
  public hazards: string;
  public riskLevel: string;
  public riskLevelId?: number;
  public controlBarriers: string;
  public protectiveBarriers: string;
  public supportBarriers: string;
  public isLotoHazard: boolean;
  public hazardOrder: number;
  public orderChange?: boolean;
  public isIncludedHazard: boolean;
  public disabled?: boolean;
  public listOfWorkStep: WorkStepsForHazards[];
  public randomGuid: string = uuid.v4();

  constructor(
    hazardId: number,
    hazards: string,
    riskLevel: string,
    riskLevelId: number,
    controlBarriers: string,
    protectiveBarriers: string,
    supportBarriers: string,
    isLotoHazard: boolean = false,
    hazardOrder: number = 0,
    isIncludedHazard: boolean = false,
    listOfWorkStep: WorkStepsForHazards[] = []
  ) {
    this.hazardId = hazardId;
    this.hazards = hazards;
    this.riskLevel = riskLevel ? riskLevel : hazardsRiskLevelList.find(x => x.nameStr === riskLevel)?.nameStr;
    this.riskLevelId = riskLevelId ? riskLevelId : hazardsRiskLevelList.find(x => x.id === this.riskLevelId)?.id;
    this.controlBarriers = controlBarriers;
    this.protectiveBarriers = protectiveBarriers;
    this.supportBarriers = supportBarriers;
    this.isLotoHazard = isLotoHazard;
    this.isIncludedHazard = isIncludedHazard;
    this.hazardOrder = hazardOrder;
    this.listOfWorkStep = listOfWorkStep;
    this.disabled = false;
  }
}

export class WorkStepDetailByWorkType extends LOTO {
  public workStep: string;
  public workStepId: number;
  public isLotoWorkStep: boolean;
  public orderValue: number;
  public orderChange?: boolean;
  public isIncludedWorkStep: boolean;
  public disabled?: boolean;
  public listOfWorkType: WorkTypesForWorkStep[];
  public randomGuid: string = uuid.v4();

  constructor(
    workStepId: number,
    workStep: string,
    equipmentType: string = '',
    equipmentModel: string = '',
    asBuildPageNumber: number = null,
    isLotoWorkStep: boolean = false,
    orderValue: number = 0,
    isIncludedWorkStep: boolean = false,
    listOfWorkType: WorkTypesForWorkStep[] = []
  ) {
    super(workStepId, workStep, equipmentType, equipmentModel, asBuildPageNumber);
    this.workStepId = workStepId;
    this.workStep = workStep;
    this.isLotoWorkStep = isLotoWorkStep;
    this.orderValue = orderValue;
    this.isIncludedWorkStep = isIncludedWorkStep;
    this.listOfWorkType = listOfWorkType;
    this.disabled = false;
  }
}

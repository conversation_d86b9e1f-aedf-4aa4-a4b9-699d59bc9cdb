import { Component, OnInit, ViewEncapsulation } from '@angular/core';
import { BsModalRef } from 'ngx-bootstrap/modal';
import { Subject } from 'rxjs';

@Component({
  selector: 'sfl-how-to-use-video-modal',
  templateUrl: './how-to-use-video-modal.component.html',
  styleUrls: ['./how-to-use-video-modal.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class HowToUseVideoModalComponent implements OnInit {
  public onClose: Subject<boolean>;
  constructor(public _bsModalRef: BsModalRef) {}

  ngOnInit(): void {}
}

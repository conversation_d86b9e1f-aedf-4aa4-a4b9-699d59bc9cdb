export class TemplateListingResponse {
  totalQESTTemplate: 0;
  listOfQESTTemplate: TemplateList[];
}
export class FormsListingResponse {
  totalQESTForm: 0;
  totalActivatedReports: 0;
  listOfQESTForm: FormsListingData[];
}

export class TemplateList {
  qestTemplateId: 0 = null;
  templateType: QESTFormTemplateTypes = QESTFormTemplateTypes.NO_QEST_FORM;
  templateName: string = '';
  templateNote: string = '';
  templateJson: string = '';
  updatedDate: string = '';
}
export class FormsListingData {
  qestFormId: 0 = null;
  formName: string = '';
  templateType: QESTFormTemplateTypes = QESTFormTemplateTypes.NO_QEST_FORM;
  formNote: string = '';
  templateName: string = '';
  customerName: string = '';
  equipmentName: string = '';
  isActive: boolean = true;
  updatedDate: string = '';
  createdBy: string = '';
  createdById: 0 = null;
}

export class AnalyticsListingResponse {
  total: 0;
  listOfDataAnalyticsTag: AnalyticsListingData[];
}

export class AnalyticsListingData {
  qestDataAnalyticsId: 0 = null;
  tagName: string = '';
  isActive: boolean = true;
  controlType: Number;
  controlDataType: Number;
  controlTypeName: string = '';
  controlDateTypeName: string = '';
}

export class TagFormModel {
  tagName: string;
  qestDataAnalyticsId?: number = 0;
  controlType: number;
  controlDataType: number;
  isActive: boolean;
  controlTypeName: string = '';
  controlDateTypeName: string = '';
  isTagInUse: boolean = false;
}

export const ControlTypeEnum = {
  checkbox: 1,
  dropdown: 2,
  textBox: 3
};

export const TagDataTypeEnum = {
  integer: 1,
  decimal: 2,
  string: 3,
  boolean: 4
};

export const itemCollectionOptions = [
  {
    id: 1,
    name: 'Customers',
    value: 'customers'
  },
  {
    id: 2,
    name: 'Portfolios',
    value: 'portfolios'
  },
  {
    id: 3,
    name: 'Sites',
    value: 'sites'
  }
];

export const defaultValueCollectionOptions = [
  // Device Information
  {
    id: 1,
    name: 'Device Name',
    value: 'deviceName'
  },
  {
    id: 2,
    name: 'Device Serial Number',
    value: 'deviceSerialNumber'
  },
  {
    id: 3,
    name: 'Device DC Load',
    value: 'deviceDCLoad'
  },
  {
    id: 4,
    name: 'Device Modbus Address',
    value: 'deviceModbusAddress'
  },
  {
    id: 5,
    name: 'Device Number Of Strings',
    value: 'deviceNumberOfStrings'
  },
  {
    id: 6,
    name: 'Device Lan IP',
    value: 'deviceLanIP'
  },
  {
    id: 7,
    name: 'Device Wan IP',
    value: 'deviceWanIP'
  },

  // General Information
  {
    id: 8,
    name: 'Current Date',
    value: 'currentDate'
  },
  {
    id: 9,
    name: 'Report Title',
    value: 'reportTitle'
  },
  {
    id: 10,
    name: 'Customer Name',
    value: 'customerName'
  },
  {
    id: 11,
    name: 'Portfolio Name',
    value: 'portfolioName'
  },
  {
    id: 12,
    name: 'Site Name',
    value: 'siteName'
  },
  {
    id: 13,
    name: 'Site Address',
    value: 'siteAddress'
  },
  {
    id: 14,
    name: 'Site City',
    value: 'siteCity'
  },
  {
    id: 15,
    name: 'Site State',
    value: 'siteState'
  },
  {
    id: 16,
    name: 'Site Zip Code',
    value: 'siteZipCode'
  },
  {
    id: 17,
    name: 'Site Latitude',
    value: 'siteLat'
  },
  {
    id: 18,
    name: 'Site Longitude',
    value: 'siteLong'
  },
  {
    id: 19,
    name: 'Site DC Size',
    value: 'siteDCSize'
  },
  {
    id: 20,
    name: 'Site AC Size',
    value: 'siteACSize'
  },
  {
    id: 21,
    name: 'Date Performed',
    value: 'datePerformed'
  },
  {
    id: 22,
    name: 'Frequency',
    value: 'frequency'
  },
  {
    id: 23,
    name: 'Manufacturer',
    value: 'manufacturer'
  },
  {
    id: 24,
    name: 'Device Model',
    value: 'deviceModel'
  },
  {
    id: 25,
    name: 'Zone',
    value: 'zoneItemList'
  }
];

export const templatesType = [1, 2, 4, 5];
export interface ButtonLabels {
  SAVE_TEMPLATE_FORM: string;
  PREVIEW: string;
  COMPLETE_DRAFT: string;
  COMPLETE: string;
  BACK: string;
}

export enum QESTFormTemplateTypes {
  NO_QEST_FORM = 0,
  QEST_INVERTER_PM = 1,
  QEST_SUMMARY_REPORT = 2,
  QEST_COVER_PAGE = 3,
  QEST_MODULE_TORQUE = 4,
  QEST_TPM_FORM = 5
}

export const PageOpenFromConstant = {
  VIEW_MODEL_SCREEN: 'viewModalScreen',
  INVERTER_MODEL_SCREEN: 'inverterModalScreen',
  FILL_INITIAL_SR: 'initialSummaryReport',
  VIEW_MODULE_TORQUE: 'viewModuleTorque',
  ZONE_MODEL_SCREEN: 'zoneModuleTorque',
  VIEW_SUMMARY_MODEL_SCREEN: 'viewSummaryModalScreen',
  FILL_INITIAL_TPM: 'initialTPMForm',
  VIEW_TPM_MODEL_SCREEN: 'viewTPMModalScreen'
};

export type TemplateOpenModelForFormConstantValues =
  (typeof TemplateOpenModelForFormConstant)[keyof typeof TemplateOpenModelForFormConstant];

export const TemplateOpenModelForFormConstant = {
  VIEW_FORMS_MODEL: 'viewForms',
  VIEW_SUMMARY_REPORTS_MODEL: 'viewSummaryReports',
  VIEW_MT_FORMS_MODEL: 'viewModuleTorqueForms',
  ZONE_MODEL_SCREEN: 'zoneModuleTorque',
  VIEW_TPM_FORMS_MODEL: 'viewTPMForms'
} as const;

export enum ElementPermissionsWithTemplateTypeEnum {
  CLONE,
  CUSTOMER_INPUT,
  EQUIPMENT_INPUT,
  SELECT_SITE_DEVICE,
  IS_ZONE_MAP_TOGGLE,
  IS_DEFAULT_FORM
}

export const ElementPermissionsWithTemplateTypeForHide = [
  {
    [QESTFormTemplateTypes.QEST_INVERTER_PM]: [
      ElementPermissionsWithTemplateTypeEnum.IS_ZONE_MAP_TOGGLE,
      ElementPermissionsWithTemplateTypeEnum.IS_DEFAULT_FORM
    ]
  },
  {
    [QESTFormTemplateTypes.QEST_SUMMARY_REPORT]: [
      ElementPermissionsWithTemplateTypeEnum.CLONE,
      ElementPermissionsWithTemplateTypeEnum.CUSTOMER_INPUT,
      ElementPermissionsWithTemplateTypeEnum.EQUIPMENT_INPUT,
      ElementPermissionsWithTemplateTypeEnum.SELECT_SITE_DEVICE,
      ElementPermissionsWithTemplateTypeEnum.IS_DEFAULT_FORM
    ]
  },
  {
    [QESTFormTemplateTypes.QEST_COVER_PAGE]: [
      ElementPermissionsWithTemplateTypeEnum.CUSTOMER_INPUT,
      ElementPermissionsWithTemplateTypeEnum.EQUIPMENT_INPUT,
      ElementPermissionsWithTemplateTypeEnum.IS_ZONE_MAP_TOGGLE,
      ElementPermissionsWithTemplateTypeEnum.IS_DEFAULT_FORM
    ]
  },
  {
    [QESTFormTemplateTypes.QEST_MODULE_TORQUE]: [ElementPermissionsWithTemplateTypeEnum.EQUIPMENT_INPUT]
  },
  {
    [QESTFormTemplateTypes.QEST_TPM_FORM]: [
      ElementPermissionsWithTemplateTypeEnum.CLONE,
      ElementPermissionsWithTemplateTypeEnum.CUSTOMER_INPUT,
      ElementPermissionsWithTemplateTypeEnum.EQUIPMENT_INPUT,
      ElementPermissionsWithTemplateTypeEnum.SELECT_SITE_DEVICE,
      ElementPermissionsWithTemplateTypeEnum.IS_DEFAULT_FORM
    ]
  }
];

export const ElementPermissionsWithPageOpenFromForHide = [
  { [PageOpenFromConstant.VIEW_MODEL_SCREEN]: [] },
  {
    [PageOpenFromConstant.VIEW_SUMMARY_MODEL_SCREEN]: [
      ElementPermissionsWithTemplateTypeEnum.CLONE,
      ElementPermissionsWithTemplateTypeEnum.CUSTOMER_INPUT,
      ElementPermissionsWithTemplateTypeEnum.EQUIPMENT_INPUT,
      ElementPermissionsWithTemplateTypeEnum.SELECT_SITE_DEVICE
    ]
  },
  {
    [PageOpenFromConstant.FILL_INITIAL_SR]: [
      ElementPermissionsWithTemplateTypeEnum.CLONE,
      ElementPermissionsWithTemplateTypeEnum.CUSTOMER_INPUT,
      ElementPermissionsWithTemplateTypeEnum.EQUIPMENT_INPUT,
      ElementPermissionsWithTemplateTypeEnum.SELECT_SITE_DEVICE
    ]
  },
  {
    [PageOpenFromConstant.VIEW_TPM_MODEL_SCREEN]: [
      ElementPermissionsWithTemplateTypeEnum.CLONE,
      ElementPermissionsWithTemplateTypeEnum.CUSTOMER_INPUT,
      ElementPermissionsWithTemplateTypeEnum.EQUIPMENT_INPUT,
      ElementPermissionsWithTemplateTypeEnum.SELECT_SITE_DEVICE
    ]
  },
  {
    [PageOpenFromConstant.FILL_INITIAL_TPM]: [
      ElementPermissionsWithTemplateTypeEnum.CLONE,
      ElementPermissionsWithTemplateTypeEnum.CUSTOMER_INPUT,
      ElementPermissionsWithTemplateTypeEnum.EQUIPMENT_INPUT,
      ElementPermissionsWithTemplateTypeEnum.SELECT_SITE_DEVICE
    ]
  }
];

export const ElementPermissionsWithPageOpenFromWithTemplateTypeForHide = [
  { [`${PageOpenFromConstant.VIEW_MODEL_SCREEN}_${QESTFormTemplateTypes.QEST_INVERTER_PM}`]: [] },
  {
    [`${PageOpenFromConstant.VIEW_SUMMARY_MODEL_SCREEN}_${QESTFormTemplateTypes.QEST_SUMMARY_REPORT}`]: [
      ElementPermissionsWithTemplateTypeEnum.CLONE,
      ElementPermissionsWithTemplateTypeEnum.CUSTOMER_INPUT,
      ElementPermissionsWithTemplateTypeEnum.EQUIPMENT_INPUT,
      ElementPermissionsWithTemplateTypeEnum.SELECT_SITE_DEVICE
    ]
  },
  {
    [`${PageOpenFromConstant.FILL_INITIAL_SR}_${QESTFormTemplateTypes.QEST_SUMMARY_REPORT}`]: [
      ElementPermissionsWithTemplateTypeEnum.CLONE,
      ElementPermissionsWithTemplateTypeEnum.CUSTOMER_INPUT,
      ElementPermissionsWithTemplateTypeEnum.EQUIPMENT_INPUT,
      ElementPermissionsWithTemplateTypeEnum.SELECT_SITE_DEVICE
    ]
  },
  {
    [`${PageOpenFromConstant.VIEW_TPM_MODEL_SCREEN}_${QESTFormTemplateTypes.QEST_TPM_FORM}`]: [
      ElementPermissionsWithTemplateTypeEnum.CLONE,
      ElementPermissionsWithTemplateTypeEnum.CUSTOMER_INPUT,
      ElementPermissionsWithTemplateTypeEnum.EQUIPMENT_INPUT,
      ElementPermissionsWithTemplateTypeEnum.SELECT_SITE_DEVICE
    ]
  },
  {
    [`${PageOpenFromConstant.FILL_INITIAL_TPM}_${QESTFormTemplateTypes.QEST_TPM_FORM}`]: [
      ElementPermissionsWithTemplateTypeEnum.CLONE,
      ElementPermissionsWithTemplateTypeEnum.CUSTOMER_INPUT,
      ElementPermissionsWithTemplateTypeEnum.EQUIPMENT_INPUT,
      ElementPermissionsWithTemplateTypeEnum.SELECT_SITE_DEVICE
    ]
  }
];

export const QEST_FORM_CONSTANT = {
  canvasBottomPadding: 90,
  canvasMovingBottomPadding: 70,
  copyObjectPadding: 120,
  copyHeightAddition: 1.5
};

export class CoverPageDetails {
  formId: number = null;
  formJson: string = '';
  templateVersion: string = '';
  filePath: string = '';
  parsedCoverPageJson: any = [];
  coverPagePdf: File;
}

export class LockedFormResponse {
  islocked: boolean;
  message: string;
}

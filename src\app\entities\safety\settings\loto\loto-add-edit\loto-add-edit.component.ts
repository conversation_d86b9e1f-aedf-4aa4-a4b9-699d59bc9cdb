import { Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { BsModalRef } from 'ngx-bootstrap/modal';
import { LOTO, LOTOBarrier, LOTOItemEmitResponse } from '../../settings.model';
import { AbstractControl, AsyncValidatorFn, FormBuilder, FormControl, FormGroup, ValidationErrors, Validators } from '@angular/forms';
import { SettingService } from '../../settings.service';
import { delay, Observable, of, Subscription } from 'rxjs';

@Component({
  selector: 'sfl-loto-add-edit',
  templateUrl: './loto-add-edit.component.html',
  styleUrls: ['./loto-add-edit.component.scss']
})
export class LotoAddEditComponent implements OnInit, OnDestroy {
  @Input() fromJhaWorkStep = false;
  @Input() lotoId = 0;
  @Input() lotoItem: LOTO = null;
  @Input() lotoBarrier: LOTOBarrier = null;
  @Input() lotoList: LOTO[] = [];
  @Output() onLotoItemEmit: EventEmitter<LOTOItemEmitResponse> = new EventEmitter<LOTOItemEmitResponse>();

  private subscription: Subscription = new Subscription();

  lotoLoader = false;
  isEditMode = false;
  isViewMode = false;
  lotoForm: FormGroup;

  constructor(
    private readonly _bsModalRef: BsModalRef,
    private readonly fb: FormBuilder,
    private readonly settingService: SettingService
  ) {}

  ngOnInit(): void {
    this.initializeFormGroup();
    console.log('LOTO Item:', this.lotoList);
    if (this.fromJhaWorkStep) {
      if (this.lotoId || this.lotoItem) {
        this.isEditMode = true;
        if (!this.lotoItem && this.lotoId) {
          this.getLOTOById(this.lotoId);
        } else {
          this.lotoList = this.lotoList.filter(item => item.id === this.lotoItem.id);
          this.lotoForm.patchValue(this.lotoItem);
        }
      } else {
        this.isEditMode = false;
        this.isViewMode = false;
      }
    }
  }

  initializeFormGroup(): void {
    this.lotoForm = this.fb.group(
      {
        id: new FormControl(0),
        name: new FormControl(),
        equipmentType: new FormControl('', Validators.compose([Validators.required])),
        equipmentModel: new FormControl('', Validators.compose([Validators.required])),
        asBuildPageNumber: new FormControl(null, Validators.compose([Validators.required]))
      },
      {
        asyncValidators: [this.lotoNameUniqueValidator],
        updateOn: 'blur' // or 'change' if you want live validation
      }
    );

    this.subscription.add(
      this.lotoForm.valueChanges.subscribe(() => {
        this.lotoForm
          .get('name')
          .setValue(
            `LOTO - (${this.lotoForm.get('equipmentType').value} - ${this.lotoForm.get('equipmentModel').value} - ${
              this.lotoForm.get('asBuildPageNumber').value
            })`,
            { emitEvent: false }
          );

        this.lotoForm.updateValueAndValidity({ onlySelf: false, emitEvent: false });
      })
    );
  }

  lotoNameUniqueValidator(existingData: LOTO[] = this.lotoList): AsyncValidatorFn {
    return (control: AbstractControl): Observable<ValidationErrors | null> => {
      const equipmentType = control.get('equipmentType')?.value;
      const equipmentModel = control.get('equipmentModel')?.value;
      const asBuildPageNumber = control.get('asBuildPageNumber')?.value;
      const currentId = control.get('id')?.value;

      const generatedName = `LOTO - (${equipmentType} - ${equipmentModel} - ${asBuildPageNumber})`;

      const isDuplicate = existingData.some(item => item.name === generatedName && item.id !== currentId);

      return of(isDuplicate ? { nonUniqueName: true } : null).pipe(delay(300));
    };
  }

  onBack(lotoItem: LOTO, fromBackButton = true): void {
    if (!fromBackButton && lotoItem) {
      const lotoItemEmitResponse = new LOTOItemEmitResponse(lotoItem, this.lotoBarrier);
      this.onLotoItemEmit.emit(lotoItemEmitResponse);
    }
    this._bsModalRef.hide();
  }

  getLOTOById(lotoId: number): void {
    this.lotoLoader = true;
    this.subscription.add(
      this.settingService.getLOTOById(lotoId).subscribe({
        next: res => {
          this.lotoForm.patchValue(res);
          this.lotoLoader = false;
        },
        error: err => {
          this.lotoLoader = false;
        }
      })
    );
  }

  createUpdateLOTO(lotoItem: LOTO): void {
    this.lotoLoader = true;
    this.subscription.add(
      this.settingService.createUpdateLOTO(lotoItem).subscribe({
        next: res => {
          this.onBack(lotoItem, false);
          this.lotoLoader = false;
        },
        error: err => {
          this.onBack(lotoItem, false);
          this.lotoLoader = false;
        }
      })
    );
  }

  onSubmit(): void {
    if (this.lotoForm.valid) {
      this.createUpdateLOTO(this.lotoForm.value);
    } else {
      this.lotoForm.markAllAsTouched();
    }
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }
}

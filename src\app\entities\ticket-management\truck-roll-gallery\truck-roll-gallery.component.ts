import { Location } from '@angular/common';
import { Component, OnDestroy, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { BsModalRef, BsModalService, ModalOptions } from 'ngx-bootstrap/modal';
import { Subscription } from 'rxjs';
import { ImageDropboxGalleryComponent } from '../../../@shared/components/image-dropbox-gallery/image-dropbox-gallery.component';
import { AppConstants } from '../../../@shared/constants';
import { CommonService } from '../../../@shared/services/common.service';
import { StorageService } from '../../../@shared/services/storage.service';
import { ModelComponent } from '../../report/model/model.component';
import { TruckRollData } from '../ticket.model';
import { TicketService } from '../ticket.service';

@Component({
  selector: 'sfl-truck-roll-gallery',
  templateUrl: './truck-roll-gallery.component.html',
  styleUrls: ['./truck-roll-gallery.component.scss']
})
export class TruckRollGalleryComponent implements OnInit, OnDestroy {
  loading = false;
  truckRollData: TruckRollData;
  redirectToTicketModalId: string;
  user: string[];
  subscription = new Subscription();
  modalRef: BsModalRef;
  fullDateFormat = AppConstants.fullDateFormat;

  constructor(
    private readonly _location: Location,
    private readonly route: ActivatedRoute,
    private readonly router: Router,
    private readonly storageService: StorageService,
    private readonly ticketService: TicketService,
    private readonly modalService: BsModalService,
    private readonly commonService: CommonService
  ) {}

  ngOnInit(): void {
    this.loading = true;
    this.route.queryParams.subscribe(params => {
      this.redirectToTicketModalId = params?.ticketIdModal;
    });
    this.route.params.subscribe(params => {
      if (params?.truckRollNo) {
        this.getTruckRollData(params?.truckRollNo);
      }
    });
    this.user = this.storageService.get('user').authorities;
  }

  private getTruckRollData(truckRollNumber: string) {
    this.loading = true;
    this.subscription.add(
      this.ticketService.getTruckRollData(truckRollNumber).subscribe({
        next: data => {
          this.truckRollData = data;
          this.loading = false;
        },
        error: () => {
          this.loading = false;
        }
      })
    );
  }

  imagePopup(index: number) {
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      class: 'modal-xl img-view-section',
      initialState: {
        imageList: this.truckRollData.imageGalleryList.filter(file => file.fileType === 'image'),
        selectedImageIndex: index
      }
    };
    this.modalRef = this.modalService.show(ModelComponent, ngModalOptions);
  }

  openDropBoxImageGallery() {
    const requestParamsConfig = {
      id: 0,
      customerId: this.truckRollData.customerId,
      portfolioId: this.truckRollData.portfolioId,
      siteId: this.truckRollData.siteId,
      entityId: null,
      parentId: null,
      entityNumber: this.truckRollData.truckRollNumber,
      activityDate: '',
      isCustomerFacing: false,
      imagePreviewId: 0,
      moduleType: 4,
      sortBy: '',
      direction: '',
      page: 0,
      itemsCount: 15
    };

    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      class: 'modal-full-dialog image-gallery-modal',
      initialState: {
        requestParamsConfig: requestParamsConfig,
        activityDate: '',
        isTruckRollGallery: true
      }
    };
    this.modalRef = this.modalService.show(ImageDropboxGalleryComponent, ngModalOptions);
    this.modalRef.content.isParentRefresh.subscribe(res => {
      if (res) {
        this.getTruckRollData(this.truckRollData.truckRollNumber);
      }
    });
  }

  goBack() {
    this.loading = true;
    if (this.redirectToTicketModalId) {
      const queryParams = { ticketIdModal: this.redirectToTicketModalId };
      this.router.navigate(['../entities/ticket/list'], { queryParams });
    } else {
      this._location.back();
    }
  }

  exportPdf(): void {
    this.loading = true;
    this.ticketService.exportTruckRollPdf(this.truckRollData.truckRollNumber).subscribe({
      next: res => {
        this.downloadFile(res);
      },
      error: () => {
        this.loading = false;
      }
    });
  }

  downloadFile(res: Blob) {
    const link = this.commonService.createObject(res, 'application/pdf');
    link.download = `TruckRollReport(${this.truckRollData.truckRollNumber}).pdf`;
    link.click();
    this.loading = false;
  }

  redirectToTicket(ticketNumber: string) {
    this.loading = true;
    this.router.navigate(['../entities/ticket/detail/view', ticketNumber]);
  }

  trackByFunction(index: number, element) {
    return element ? index : null;
  }

  ngOnDestroy(): void {
    if (this.modalRef) {
      this.modalRef.hide();
    }
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}

import { DatePipe } from '@angular/common';
import { Component, Input, OnInit, TemplateRef, ViewChild, ViewEncapsulation } from '@angular/core';
import { Router } from '@angular/router';
import { BsModalRef, BsModalService, ModalOptions } from 'ngx-bootstrap/modal';
import { forkJoin, Subject, Subscription } from 'rxjs';
import { CommonFilter } from '../../../../@shared/components/filter/common-filter.model';
import { AppConstants } from '../../../../@shared/constants/app.constant';
import { Dropdown } from '../../../../@shared/models/dropdown.model';
import { AlertService } from '../../../../@shared/services/alert.service';
import { StorageService } from '../../../../@shared/services/storage.service';
import {
  fieldNameMap,
  idMappings,
  NavigationBack,
  TicketBillingStatusesResponse,
  TicketBulkEdit,
  TicketPriorityMapping,
  Tickets,
  TicketsList
} from '../../ticket.model';
import { TicketService } from '../../ticket.service';

@Component({
  selector: 'sfl-bulk-ticket-edit-action',
  templateUrl: './bulk-ticket-edit-action.component.html',
  styleUrls: ['./bulk-ticket-edit-action.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class BulkTicketEditActionComponent implements OnInit {
  @Input() selectedBulkAction;
  @Input() filterModel: CommonFilter;
  @Input() total: number;
  public onClose: Subject<boolean> = new Subject();
  pageSize = AppConstants.rowsPerPage;
  currentPage = 1;
  tickets: Tickets[];
  @ViewChild('truckRollModal') truckRollModal: TemplateRef<void>;
  selectedBulkEditTickets: Tickets[] = [];
  loading = false;
  selectedTicketForTruckRoll: Tickets;
  subscription: Subscription = new Subscription();
  sortOptionList = {
    Number: 'asc',
    Priority: 'asc',
    CustomerPortfolio: 'asc',
    Site: 'asc',
    Device: 'asc',
    Close: 'asc',
    RegionName: 'asc',
    SubRegionName: 'asc',
    Open: 'desc'
  };
  fullDateFormat = AppConstants.fullDateFormat;
  dateFormat = AppConstants.momentDateFormat;
  modalRef: BsModalRef;
  userRole = this.storageService.get('user').authorities;
  currentStep: number = 1;
  totalSteps: number = 5;
  isMasterSel = false;
  disabledResolved = false;
  ticketEditForm: TicketBulkEdit = new TicketBulkEdit();
  minDate: Date;
  ticketBillingStatuses: TicketBillingStatusesResponse[] = [];
  priorityList = TicketPriorityMapping;
  lossTypeList: Dropdown[] = [];
  fieldNameMap = fieldNameMap;
  idMappings = idMappings;
  completedCount = 0;
  apiCount = 0;
  failedEntityNumber = [];
  fieldAddedForBulk = [
    { name: 'Ticket status', value: false, id: 1 },
    { name: 'Billing status', value: false, id: 2 },
    { name: 'Priority', value: false, id: 3 },
    { name: 'Issue', value: false, id: 4 },
    { name: 'Production Loss', value: false, id: 5 },
    { name: 'Add Comment', value: false, id: 6 },
    { name: 'Add Activity', value: false, id: 7 }
  ];
  availableFields = {
    ticketStatus: false,
    billingStatus: false,
    priority: false,
    issue: false,
    productionLoss: false,
    comment: false,
    activity: false
  };
  // activityDetails: ActivityDetails = new ActivityDetails();
  constructor(
    public _bsModalRef: BsModalRef,
    private readonly router: Router,
    private readonly datePipe: DatePipe,
    private readonly ticketService: TicketService,
    private readonly storageService: StorageService,
    private readonly modalService: BsModalService,
    private readonly alertService: AlertService
  ) {}

  ngOnInit(): void {
    this.getAllTicketList();
    const apiArray = [this.ticketService.getTicketBillingStatuses(), this.ticketService.getlossTypeList()];
    const tempObj = ['ticketBillingStatuses', 'lossTypeList'];
    forkJoin(apiArray).subscribe({
      next: (res: any) => {
        for (const [index, value] of tempObj.entries()) {
          this[value] = res[index];
        }
      }
    });
  }

  setActivityMinDate() {
    this.minDate = new Date(Math.max(...this.selectedBulkEditTickets.map(ticket => new Date(ticket.open).getTime())));
  }

  isAnyFiledAdded() {
    return Object.values(this.availableFields).includes(true);
  }

  addRemoveBulkEditFields(isChecked: boolean, fieldId): void {
    const ticketStatusField = this.fieldAddedForBulk.find(field => field.id === fieldId);
    if (ticketStatusField) {
      ticketStatusField.value = isChecked;
    }
    if (!isChecked) {
      switch (fieldId) {
        case 1:
          this.ticketEditForm.ticketStatus = null;
          this.ticketEditForm.closedDate = null;
          break;
        case 2:
          this.ticketEditForm.billingStatus = null;
          break;
        case 3:
          this.ticketEditForm.priority = null;
          break;
        case 4:
          this.ticketEditForm.issueAction = null;
          this.ticketEditForm.issueTxt = '';
          break;
        case 5:
          this.ticketEditForm.productionLoss = null;
          this.ticketEditForm.affectedKWAC = null;
          this.ticketEditForm.lossType = null;
          this.ticketEditForm.estKWHLoss = null;
          break;
        case 6:
          this.ticketEditForm.comment = '';
          break;
        case 7:
          this.ticketEditForm.activityDate = null;
          this.ticketEditForm.isResolved = false;
          this.ticketEditForm.materials = '';
          this.ticketEditForm.description = '';
          this.ticketEditForm.materialCost = null;
          break;

        default:
          break;
      }
    }
  }

  getTableData() {
    const excludedKeys = ['ticketIds', 'isSendEmail'];

    return Object.keys(this.ticketEditForm || {})
      .filter(key => this.shouldIncludeKey(key, excludedKeys))
      .map(key => ({
        fieldName: this.fieldNameMap[key] || key,
        value: this.getDisplayName(key, this.ticketEditForm[key]),
        action: key === 'comment' ? 'Added' : 'Updated'
      }))
      .filter(item => this.isValidValue(item.value));
  }

  private shouldIncludeKey(key: string, excludedKeys: string[]): boolean {
    if (key === 'isResolved') {
      return this.availableFields?.activity;
    }
    return !excludedKeys.includes(key);
  }

  private isValidValue(value: any): boolean {
    return value !== null && value !== '' && value !== undefined;
  }

  getDisplayName(fieldName: string, fieldValue: any): string {
    if (this.idMappings[fieldName] && this.idMappings[fieldName][fieldValue] !== undefined) {
      return this.idMappings[fieldName][fieldValue];
    } else if (fieldName === 'billingStatus') {
      return this.getBillingStatusDescription(fieldValue);
    } else if (fieldName === 'lossType') {
      return this.getLossTypeName(fieldValue);
    } else if (fieldName === 'closedDate' || fieldName === 'activityDate') {
      return this.datePipe.transform(fieldValue, AppConstants.fullDateFormat);
    } else if (fieldName === 'productionLoss') {
      return this.getProductionLossName(fieldValue);
    }
    return fieldValue;
  }

  getProductionLossName(productionLoss: number) {
    if (productionLoss === 1) {
      return 'No';
    } else if (productionLoss === 0) {
      return 'Yes';
    } else {
      return '--';
    }
  }

  getBillingStatusDescription(statusId: number) {
    const billingStatus = this.ticketBillingStatuses.find(status => status.ticketBillingStatusID === statusId);
    return billingStatus ? billingStatus.description : '';
  }
  getLossTypeName(lossTypeId: number) {
    const billingStatus = this.lossTypeList.find(status => status.id === lossTypeId);
    return billingStatus ? billingStatus.name : '';
  }

  SaveBulkAction() {
    this.currentStep = 5;
    const ticketIds = this.selectedBulkEditTickets.map(item => item.id);
    const requestModel = {
      ticketIds: ticketIds,
      ticketStatus: this.ticketEditForm.ticketStatus,
      closedDate: this.datePipe.transform(this.ticketEditForm.closedDate, AppConstants.fullDateFormat),
      billingStatus: this.ticketEditForm.billingStatus,
      priority: this.ticketEditForm.priority,
      issueAction: this.ticketEditForm.issueAction,
      issueTxt: this.ticketEditForm.issueTxt,
      productionLoss: this.ticketEditForm.productionLoss,
      affectedKWAC: this.ticketEditForm.affectedKWAC,
      lossType: this.ticketEditForm.lossType,
      estKWHLoss: this.ticketEditForm.estKWHLoss,
      comment: this.ticketEditForm.comment,
      activity: this.availableFields.activity
        ? {
            activityDate: this.datePipe.transform(this.ticketEditForm.activityDate, AppConstants.fullDateFormat),
            description: this.ticketEditForm.description,
            isResolved: this.ticketEditForm.isResolved,
            materials: this.ticketEditForm.materials,
            materialCost: this.ticketEditForm.materialCost
          }
        : null,
      isSendEmail: this.ticketEditForm.isSendEmail
    };
    this.subscription.add(
      this.ticketService.saveBulkTicketUpdateAction(requestModel).subscribe({
        next: res => {
          if (res.status === 1 || res.status === -1) {
            this.apiCount = this.selectedBulkEditTickets.length;
            this.completedCount = this.selectedBulkEditTickets.length;
            res.status === 1 ? this.alertService.showSuccessToast(res.message) : this.alertService.showErrorToast(res.message);
            if (res.entityIds.length > 0) {
              this.failedEntityNumber = this.selectedBulkEditTickets
                .filter(item => res.entityIds.includes(item.id))
                .map(item => item.ticketNumber);
            }
          } else {
            this.updateSingleTicket(requestModel);
          }
        },
        error: e => {
          this.loading = false;
        }
      })
    );
  }

  updateSingleTicket(requestModel) {
    const failedId = [];
    for (const element of this.selectedBulkEditTickets) {
      const singleModel = {
        ...requestModel,
        ticketIds: [element.id]
      };
      this.subscription.add(
        this.ticketService.saveBulkTicketUpdateAction(singleModel).subscribe({
          next: res => {
            this.apiCount++;
            if (res.status === 1) {
              this.completedCount++;
            } else {
              if (res.entityIds.length > 0) {
                this.failedEntityNumber.push(res.entityIds);
              }
            }
            if (this.completedCount === this.selectedBulkEditTickets.length) {
              this.alertService.showSuccessToast('Bulk Ticket Update Successfully.');
            }
          },
          error: e => {
            this.loading = false;
          }
        })
      );
    }
    if (failedId.length > 0) {
      this.failedEntityNumber = this.selectedBulkEditTickets.filter(item => failedId.includes(item.id)).map(item => item.ticketNumber);
    }
  }

  public onCancel(): void {
    this.onClose.next(true);
    this._bsModalRef.hide();
  }

  closeModel() {
    this._bsModalRef.hide();
    this.selectedBulkEditTickets = [];
    this.ticketEditForm = new TicketBulkEdit();
  }

  proceedNextStep(bulkEditForm) {
    // Validate form on the current step
    if (this.currentStep === 2) {
      if (bulkEditForm?.form?.valid) {
        this.moveToNextStep();
      } else {
        this.markFormControlsAsTouched(bulkEditForm);
        return;
      }
    } else {
      if (this.currentStep == 1) {
        this.disabledResolved = this.selectedBulkEditTickets.some(ticket => ticket.isResolve);
        this.setActivityMinDate();
      }
      this.moveToNextStep();
    }
  }

  moveToNextStep() {
    if (this.currentStep < this.totalSteps) {
      this.currentStep++;
    }
  }

  markFormControlsAsTouched(form) {
    if (form && form.form && form.form.controls) {
      Object.keys(form.form.controls).forEach(key => {
        const control = form.form.controls[key];
        control.markAsTouched();
        control.updateValueAndValidity();
      });
    }
  }

  proceedBackStep() {
    if (this.currentStep > 1) {
      this.currentStep--;
    } else {
      this.closeModel();
    }
  }

  sort(sortBy: string, changeSort: string) {
    if (changeSort === 'asc') {
      changeSort = 'desc';
    } else {
      changeSort = 'asc';
    }
    this.sortOptionList[sortBy] = changeSort;
    this.filterModel.sortBy = sortBy;
    this.filterModel.direction = changeSort;
    this.currentPage = 1;
    this.getAllTicketList();
  }

  openTruckRollModal(item: Tickets, template: TemplateRef<void>) {
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      class: 'modal-md'
    };
    this.selectedTicketForTruckRoll = item;
    this.modalRef = this.modalService.show(template, ngModalOptions);
  }

  onPageChange(obj) {
    this.currentPage = obj;
  }

  getAllTicketList() {
    this.loading = true;
    const model: CommonFilter = JSON.parse(JSON.stringify(this.filterModel));
    if (this.filterModel.openDate && this.filterModel.openDate.start && this.filterModel.openDate.end) {
      model.openDate.start = this.datePipe.transform(this.filterModel.openDate.start, AppConstants.fullDateFormat);
      model.openDate.end = this.datePipe.transform(this.filterModel.openDate.end, AppConstants.fullDateFormat);
    } else {
      this.filterModel.openDate = null;
      model.openDate = null;
    }
    if (this.filterModel.closeDate && this.filterModel.closeDate.start && this.filterModel.closeDate.end) {
      model.closeDate.start = this.datePipe.transform(this.filterModel.closeDate.start, AppConstants.fullDateFormat);
      model.closeDate.end = this.datePipe.transform(this.filterModel.closeDate.end, AppConstants.fullDateFormat);
    } else {
      this.filterModel.closeDate = null;
      model.closeDate = null;
    }
    if (this.filterModel.activityRange && this.filterModel.activityRange.start && this.filterModel.activityRange.end) {
      model.activityRange.start = this.datePipe.transform(this.filterModel.activityRange.start, AppConstants.fullDateFormat);
      model.activityRange.end = this.datePipe.transform(this.filterModel.activityRange.end, AppConstants.fullDateFormat);
    } else {
      this.filterModel.activityRange = null;
      model.activityRange = null;
    }

    model.itemsCount = 1000;
    model.getAllStatusListBetweenDates = false;

    this.ticketService.getAllTicketList(model).subscribe({
      next: (res: TicketsList) => {
        this.bindTickets(res);
      },
      error: e => {
        this.loading = false;
      }
    });
  }

  bindTickets(res: TicketsList) {
    this.tickets =
      res?.ticketLists?.map(ticket => ({
        ...ticket,
        show: false,
        isSelected: this.selectedBulkEditTickets.some(selectedFile => selectedFile.id === ticket.id)
      })) || [];

    this.total = this.tickets?.length;
    this.isMasterSel = this.tickets.every(ticket => ticket.isSelected);
    this.disabledResolved = this.selectedBulkEditTickets.some(ticket => ticket.isResolve);

    this.loading = false;
  }

  selectDeselectAll() {
    const selectedIds = new Set(this.selectedBulkEditTickets.map(file => file.id));

    this.tickets.forEach(ticket => {
      ticket.isSelected = this.isMasterSel;

      if (this.isMasterSel) {
        if (!selectedIds.has(ticket.id)) {
          this.selectedBulkEditTickets.push(ticket);
        }
      } else {
        this.selectedBulkEditTickets = this.selectedBulkEditTickets.filter(file => file.id !== ticket.id);
      }
    });
  }

  singleTicketCheckChanged(ticket: Tickets) {
    if (ticket.isSelected) {
      if (!this.selectedBulkEditTickets.some(file => file.id === ticket.id)) {
        this.selectedBulkEditTickets.push(ticket);
      }
    } else {
      this.selectedBulkEditTickets = this.selectedBulkEditTickets.filter(file => file.id !== ticket.id);
    }

    this.isMasterSel = this.tickets.every(ticket => ticket.isSelected);
  }

  openLink(ticketNumber: string, inNewWindow: boolean) {
    const url = this.router.serializeUrl(
      this.router.createUrlTree(['../entities/ticket/detail/view', `${ticketNumber}`], { queryParams: { back: NavigationBack.TICKETS } })
    );
    if (inNewWindow) {
      window.open(url, '_blank', 'width=' + screen.availWidth + ',height=' + screen.availHeight);
    } else {
      window.open(url, '_blank');
    }
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}

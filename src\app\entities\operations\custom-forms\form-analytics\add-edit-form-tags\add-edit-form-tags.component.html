<div class="alert-box">
  <div class="modal-header">
    <div class="d-flex align-items-center w-100">
      <h6 class="m-0">{{ isEdit ? 'Edit Tag' : 'Add Tag' }}</h6>
      <div class="ms-auto">
        <button type="button" class="close" aria-label="Close" (click)="_bsModalRef.hide()">
          <span aria-hidden="true"><em class="fa-solid fa-xmark fa-xl"></em></span>
        </button>
      </div>
    </div>
  </div>
  <div class="modal-body ModalBody detail-modal-body" [nbSpinner]="loading" nbSpinnerStatus="primary" nbSpinnerSize="large">
    <form name="addUpdate" #addUpdateForm="ngForm" aria-labelledby="title" autocomplete="off">
      <div class="row">
        <div class="col-12 mb-2">
          <label class="label" for="tagName">Tag Name<span class="ms-1 text-danger">*</span></label>
          <input
            nbInput
            name="tagName"
            #tagName="ngModel"
            class="disabled-input form-control"
            fullWidth
            placeholder="Tag Name"
            [(ngModel)]="tagFormModel.tagName"
            required
          />
          <sfl-error-msg [control]="tagName" [isFormSubmitted]="addUpdateForm?.submitted" fieldName="Tag Name"></sfl-error-msg>
        </div>
        <div class="col-12 mt-1">
          <div class="row">
            <div class="col-12 mb-2">
              <label class="label" for="controlType">
                Control Type
                <span class="ms-1 text-danger">*</span>
              </label>
              <ng-select
                id="controlType"
                name="controlType"
                bindLabel="name"
                bindValue="id"
                [items]="controlTypeList"
                notFoundText="No Control Type Found"
                placeholder="Select Control Type"
                (change)="getTagDataTypeList()"
                [clearable]="true"
                [(ngModel)]="tagFormModel.controlType"
                #controlType="ngModel"
                [closeOnSelect]="true"
                [disabled]="tagFormModel.isTagInUse"
                required
              >
              </ng-select>
              <sfl-error-msg [control]="controlType" [isFormSubmitted]="addUpdateForm?.submitted" fieldName="Control Type"></sfl-error-msg>
            </div>
            <div class="col-12 mb-2">
              <label class="label" for="tagType"> Tag Type <span class="ms-1 text-danger">*</span></label>
              <ng-select
                id="tagType"
                name="tagType"
                bindLabel="name"
                bindValue="id"
                [items]="tagTypeList"
                notFoundText="No Tag Type Found"
                placeholder="Select Tag Type"
                [clearable]="true"
                [(ngModel)]="tagFormModel.controlDataType"
                #tagType="ngModel"
                [closeOnSelect]="true"
                [disabled]="tagFormModel.isTagInUse"
                required
              >
              </ng-select>
              <sfl-error-msg [control]="tagType" [isFormSubmitted]="addUpdateForm?.submitted" fieldName="Tag Type"></sfl-error-msg>
            </div>
          </div>
        </div>
      </div>
    </form>
  </div>
  <div class="modal-footer ModalFooter">
    <button nbButton status="basic" size="medium" (click)="onCancel()" type="button">Cancel</button>
    <button nbButton status="primary" [disabled]="!addUpdateForm.valid" (click)="onSubmit()" size="medium" id="tagSubmit" type="submit">
      {{ isEdit ? 'Update' : 'Save' }}
    </button>
  </div>
</div>

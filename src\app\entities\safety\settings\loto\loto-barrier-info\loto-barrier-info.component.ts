import { Component, OnD<PERSON>roy, OnInit } from '@angular/core';
import { LOTOBarrier } from '../../settings.model';
import { Subscription } from 'rxjs';
import { FormGroup, FormControl, Validators, FormBuilder } from '@angular/forms';
import { SettingService } from '../../settings.service';
import { hazardsRiskLevelList } from '../../../../../@shared/models/jha.model';

@Component({
  selector: 'sfl-loto-barrier-info',
  templateUrl: './loto-barrier-info.component.html',
  styleUrls: ['./loto-barrier-info.component.scss']
})
export class LotoBarrierInfoComponent implements OnInit, OnDestroy {
  private subscription: Subscription = new Subscription();
  lotoBarrierLoader = false;
  riskLevelList = hazardsRiskLevelList;
  lotoBarrierForm: FormGroup;

  constructor(private readonly settingService: SettingService, private readonly fb: FormBuilder) {}

  ngOnInit(): void {
    this.initializeFormGroup();
    this.getLOTOBarrier();
  }

  initializeFormGroup(): void {
    this.lotoBarrierForm = this.fb.group({
      id: new FormControl(),
      name: new FormControl(''),
      riskLevel: new FormControl(null, Validators.compose([Validators.required])),
      controlBarrier: new FormControl(''),
      supportBarrier: new FormControl(''),
      protectiveBarrier: new FormControl(''),
      riskLevelStr: new FormControl()
    });
  }

  getLOTOBarrier(): void {
    this.lotoBarrierLoader = true;
    this.subscription.add(
      this.settingService.getLOTOBarrier().subscribe({
        next: (res: LOTOBarrier) => {
          this.lotoBarrierForm.patchValue(res);
          this.lotoBarrierLoader = false;
        },
        error: (e: any) => {
          this.lotoBarrierLoader = false;
        }
      })
    );
  }

  createUpdateLOTOBarrier(lotoBarrierItem: LOTOBarrier): void {
    this.lotoBarrierLoader = true;
    this.subscription.add(
      this.settingService.createUpdateLOTOBarrier(lotoBarrierItem).subscribe({
        next: (res: LOTOBarrier) => {
          this.lotoBarrierLoader = false;
        },
        error: (e: any) => {
          this.lotoBarrierLoader = false;
        }
      })
    );
  }

  onCreateUpdateLOTOBarrier(): void {
    if (this.lotoBarrierForm.valid) {
      this.createUpdateLOTOBarrier(this.lotoBarrierForm.value);
    } else {
      this.lotoBarrierForm.markAllAsTouched();
    }
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }
}

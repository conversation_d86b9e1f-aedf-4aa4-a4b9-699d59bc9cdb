import { Component, OnInit } from '@angular/core';
import { BsModalRef, BsModalService, ModalOptions } from 'ngx-bootstrap/modal';
import { Subscription, forkJoin } from 'rxjs';
import { ConfirmDialogComponent } from '../../../@shared/components/confirm-dialog/confirm-dialog.component';
import { CommonFilter } from '../../../@shared/components/filter/common-filter.model';
import { FilterDetails } from '../../../@shared/components/filter/filter.model';
import { AppConstants } from '../../../@shared/constants';
import { UserRole, UserRoleIdMap } from '../../../@shared/models/user.model';
import { AlertService } from '../../../@shared/services';
import { StorageService } from '../../../@shared/services/storage.service';
import { UserService } from '../../user-management/user.service';
import { RegionMappingModalComponent } from './region-mapping-modal/region-mapping-modal.component';
import { RegionMappingService } from './region-mapping.service';

@Component({
  selector: 'sfl-region-mapping',
  templateUrl: './region-mapping.component.html',
  styleUrls: ['./region-mapping.component.scss']
})
export class RegionMappingComponent implements OnInit {
  loading = false;
  modalRef: BsModalRef;
  countyFilterDetails: FilterDetails = new FilterDetails();
  regionFilterDetails: FilterDetails = new FilterDetails();
  subRegionFilterDetails: FilterDetails = new FilterDetails();
  countyFilterModel: CommonFilter = new CommonFilter();
  regionFilterModel: CommonFilter = new CommonFilter();
  subRegionFilterModel: CommonFilter = new CommonFilter();
  selectedTab: 'Region' | 'Subregion' | 'County' = 'Region';
  subscription: Subscription = new Subscription();
  usersList: any[];
  regionDropdownList: any[];
  subRegionDropdownList: any[];
  viewRegionPage = 'regionPage';
  viewSubRegionPage = 'subRegionPage';
  viewCountyPage = 'countyPage';
  viewRegionFilterSection = 'regionFilterSection';
  viewSubRegionFilterSection = 'subRegionFilterSection';
  viewCountyFilterSection = 'countyFilterSection';
  isRegionFilterDisplay = false;
  isSubRegionFilterDisplay = false;
  isCountyFilterDisplay = false;
  tableDetails = [
    {
      id: 1,
      accordionHeader: 'Region',
      columns: [
        { field: 'regionName', header: 'Region' },
        { field: 'userName', header: 'FOM' },
        { field: 'siteCount', header: 'Site Count' },
        { field: 'action', header: 'Action' }
      ],
      data: [],
      totalCount: 0,
      currentPage: 1,
      pageSize: AppConstants.rowsPerPage,
      sortOptionList: {
        regionName: 'asc',
        userName: 'asc',
        siteCount: 'asc'
      }
    },
    {
      id: 2,
      accordionHeader: 'Subregion',
      columns: [
        { field: 'regionName', header: 'Region' },
        { field: 'subRegionName', header: 'Subregion' },
        { field: 'userName', header: 'FOM' },
        { field: 'siteCount', header: 'Site Count' },
        { field: 'action', header: 'Action' }
      ],
      data: [],
      totalCount: 0,
      currentPage: 1,
      pageSize: AppConstants.rowsPerPage,
      sortOptionList: {
        regionName: 'asc',
        subRegionName: 'asc',
        userName: 'asc',
        siteCount: 'asc'
      }
    },
    {
      id: 3,
      accordionHeader: 'County',
      columns: [
        { field: 'regionName', header: 'Region' },
        { field: 'subRegionName', header: 'Subregion' },
        { field: 'countyName', header: 'County' },
        { field: 'countyFIPSCode', header: 'County FIPS' },
        { field: 'userName', header: 'FOM' },
        { field: 'siteCount', header: 'Site Count' },
        { field: 'action', header: 'Action' }
      ],
      data: [],
      totalCount: 0,
      currentPage: 1,
      pageSize: AppConstants.rowsPerPage,
      sortOptionList: {
        countyName: 'asc',
        countyFIPSCode: 'asc',
        regionName: 'asc',
        subRegionName: 'asc',
        userName: 'asc',
        siteCount: 'asc'
      }
    }
  ];

  constructor(
    private readonly userService: UserService,
    private readonly alertService: AlertService,
    private readonly modalService: BsModalService,
    private readonly storageService: StorageService,
    private readonly regionMappingService: RegionMappingService
  ) {}

  ngOnInit() {
    const regionFilter = this.storageService.get(this.viewRegionPage),
      subRegionFilter = this.storageService.get(this.viewSubRegionPage),
      countyFilter = this.storageService.get(this.viewCountyPage),
      regionFilterSection = this.storageService.get(this.viewRegionFilterSection),
      subRegionFilterSection = this.storageService.get(this.viewSubRegionFilterSection),
      countyFilterSection = this.storageService.get(this.viewCountyFilterSection);

    this.initFilterDetails();

    this.isRegionFilterDisplay = regionFilterSection || false;
    this.isSubRegionFilterDisplay = subRegionFilterSection || false;
    this.isCountyFilterDisplay = countyFilterSection || false;
    this.regionFilterModel.sortBy = 'regionName';
    this.subRegionFilterModel.sortBy = 'subRegionName';
    this.countyFilterModel.sortBy = 'countyName';

    if (regionFilter) {
      this.regionFilterModel = JSON.parse(JSON.stringify(regionFilter));

      if (this.regionFilterModel.itemsCount) {
        this.tableDetails[0].pageSize = this.regionFilterModel.itemsCount;
      }

      if (this.regionFilterModel.page) {
        this.tableDetails[0].currentPage = this.regionFilterModel.page + 1;
      }
    }
    if (subRegionFilter) {
      this.subRegionFilterModel = JSON.parse(JSON.stringify(subRegionFilter));

      if (this.subRegionFilterModel.itemsCount) {
        this.tableDetails[1].pageSize = this.subRegionFilterModel.itemsCount;
      }

      if (this.subRegionFilterModel.page) {
        this.tableDetails[1].currentPage = this.subRegionFilterModel.page + 1;
      }
    }
    if (countyFilter) {
      this.countyFilterModel = JSON.parse(JSON.stringify(countyFilter));

      if (this.countyFilterModel.itemsCount) {
        this.tableDetails[2].pageSize = this.countyFilterModel.itemsCount;
      }

      if (this.countyFilterModel.page) {
        this.tableDetails[2].currentPage = this.countyFilterModel.page + 1;
      }
    }
  }

  initFilterDetails() {
    let filterItem = JSON.parse(JSON.stringify(AppConstants.FILTERS)),
      fields = ['regionFilterDetails', 'subRegionFilterDetails', 'countyFilterDetails'];

    filterItem.SEARCH_BOX.show = true;

    fields.forEach(field => {
      this[field].filter_section_name =
        field === 'regionFilterDetails'
          ? this.viewRegionFilterSection
          : field === 'subRegionFilterDetails'
          ? this.viewSubRegionFilterSection
          : this.viewCountyFilterSection;
      this[field].page_name =
        field === 'regionFilterDetails'
          ? this.viewRegionPage
          : field === 'subRegionFilterDetails'
          ? this.viewSubRegionPage
          : this.viewCountyPage;
      this[field].api = [];
      this[field].default_sort =
        field === 'regionFilterDetails' ? 'regionName' : field === 'subRegionFilterDetails' ? 'subRegionName' : 'countyName';
      this[field].filter_item = JSON.parse(JSON.stringify(filterItem));
    });
  }

  getRegionSubregionAndCountyList(selectedTab?: string) {
    this.loading = true;

    if (selectedTab === 'Region') {
      this.regionMappingService
        .getRegionsList({
          search: this.regionFilterModel.search,
          sortBy: this.regionFilterModel.sortBy,
          direction: this.regionFilterModel.direction,
          page: this.regionFilterModel.page,
          itemsCount: this.regionFilterModel.itemsCount
        })
        .subscribe({
          next: response => {
            this.tableDetails[0].data = response.regions;
            this.tableDetails[0].totalCount = response.totalRegion;
            this.loading = false;
            this.storageService.set(this.viewRegionPage, this.regionFilterModel);
          },
          error: () => (this.loading = false)
        });
    }
    if (selectedTab === 'Subregion') {
      this.regionMappingService
        .getSubRegionsList({
          search: this.subRegionFilterModel.search,
          sortBy: this.subRegionFilterModel.sortBy,
          direction: this.subRegionFilterModel.direction,
          page: this.subRegionFilterModel.page,
          itemsCount: this.subRegionFilterModel.itemsCount
        })
        .subscribe({
          next: response => {
            this.tableDetails[1].data = response.subRegions;
            this.tableDetails[1].totalCount = response.totalSubRegion;
            this.loading = false;

            this.storageService.set(this.viewSubRegionPage, this.subRegionFilterModel);
          },
          error: () => (this.loading = false)
        });
    }
    if (selectedTab === 'County') {
      this.regionMappingService
        .getCountiesList({
          search: this.countyFilterModel.search,
          sortBy: this.countyFilterModel.sortBy,
          direction: this.countyFilterModel.direction,
          page: this.countyFilterModel.page,
          itemsCount: this.countyFilterModel.itemsCount
        })
        .subscribe({
          next: response => {
            this.tableDetails[2].data = response.county;
            this.tableDetails[2].totalCount = response.totalCounty;
            this.loading = false;

            this.storageService.set(this.viewCountyPage, this.countyFilterModel);
          },
          error: () => (this.loading = false)
        });
    }
  }

  getUsersAndRegionDropdownList() {
    this.loading = true;
    const userRoles = Object.entries(UserRoleIdMap)
      .filter(([roleKey, id]) => roleKey !== UserRole.CUST && roleKey !== UserRole.CONT)
      .map(([_, id]) => id);
    forkJoin([
      this.userService.getUserByRoles(userRoles),
      this.regionMappingService.getRegionDropdownList(),
      this.regionMappingService.getSubRegionDropdownList()
    ]).subscribe({
      next: (response: any[]) => {
        this.usersList = response[0];
        this.regionDropdownList = response[1];
        this.subRegionDropdownList = response[2];

        this.getRegionSubregionAndCountyList(this.selectedTab);
      },
      error: () => (this.loading = false)
    });
  }

  addUpdateRecord(type: 'Add' | 'Update', title: string, id?: number) {
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      class: 'modal-lg modal-dialog-right',
      initialState: {
        type,
        title,
        id: type === 'Update' ? id : 0,
        regionList: this.regionDropdownList,
        subRegionList: this.subRegionDropdownList,
        usersList: this.usersList
      }
    };

    this.modalRef = this.modalService.show(RegionMappingModalComponent, ngModalOptions);
    this.modalRef.content.onClose.subscribe({
      next: response => {
        if (response) {
          this.getRegionSubregionAndCountyList(this.selectedTab);
        }
      }
    });
  }

  onDeleteRecord(id: number) {
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      initialState: {
        message: 'Are you sure you want to delete this record?'
      }
    };

    this.modalRef = this.modalService.show(ConfirmDialogComponent, ngModalOptions);

    this.modalRef.content.onClose.subscribe({
      next: result => {
        if (result) {
          this.loading = true;

          if (this.selectedTab === 'Region') {
            this.subscription.add(
              this.regionMappingService.deleteRegion(id).subscribe({
                next: response => {
                  this.alertService.showSuccessToast(response.message);
                  this.getRegionSubregionAndCountyList(this.selectedTab);
                },
                error: e => (this.loading = false)
              })
            );
          } else if (this.selectedTab === 'Subregion') {
            this.subscription.add(
              this.regionMappingService.deleteSubRegion(id).subscribe({
                next: response => {
                  this.alertService.showSuccessToast(response.message);
                  this.getRegionSubregionAndCountyList(this.selectedTab);
                },
                error: e => (this.loading = false)
              })
            );
          }
        }
      }
    });
  }

  refreshList(filterParams: CommonFilter) {
    if (this.selectedTab === 'Region') {
      this.regionFilterModel.search = filterParams.search;
      this.tableDetails[0].currentPage = filterParams.page;
    } else if (this.selectedTab === 'Subregion') {
      this.subRegionFilterModel.search = filterParams.search;
      this.tableDetails[1].currentPage = filterParams.page;
    } else {
      this.countyFilterModel.search = filterParams.search;
      this.tableDetails[2].currentPage = filterParams.page;
    }

    this.getRegionSubregionAndCountyList(this.selectedTab);
  }

  onChangeSize() {
    if (this.selectedTab === 'Region') {
      this.regionFilterModel.page = 0;
      this.regionFilterModel.itemsCount = this.tableDetails[0].pageSize;
      this.tableDetails[0].currentPage = 0;
    } else if (this.selectedTab === 'Subregion') {
      this.subRegionFilterModel.page = 0;
      this.subRegionFilterModel.itemsCount = this.tableDetails[1].pageSize;
      this.tableDetails[1].currentPage = 0;
    } else {
      this.countyFilterModel.page = 0;
      this.countyFilterModel.itemsCount = this.tableDetails[2].pageSize;
      this.tableDetails[2].currentPage = 0;
    }

    this.getRegionSubregionAndCountyList(this.selectedTab);
  }

  onPageChange(obj) {
    if (this.selectedTab === 'Region') {
      this.tableDetails[0].currentPage = obj;
      this.regionFilterModel.page = this.tableDetails[0].currentPage - 1;
    } else if (this.selectedTab === 'Subregion') {
      this.tableDetails[1].currentPage = obj;
      this.subRegionFilterModel.page = this.tableDetails[1].currentPage - 1;
    } else {
      this.tableDetails[2].currentPage = obj;
      this.countyFilterModel.page = this.tableDetails[2].currentPage - 1;
    }

    this.getRegionSubregionAndCountyList(this.selectedTab);
  }

  sort(sortBy: string, changeSort: string) {
    if (changeSort === 'asc') {
      changeSort = 'desc';
    } else {
      changeSort = 'asc';
    }

    if (this.selectedTab === 'Region') {
      this.tableDetails[0].sortOptionList[sortBy] = changeSort;
      this.regionFilterModel.sortBy = sortBy;
      this.regionFilterModel.direction = changeSort;
    } else if (this.selectedTab === 'Subregion') {
      this.tableDetails[1].sortOptionList[sortBy] = changeSort;
      this.subRegionFilterModel.sortBy = sortBy;
      this.subRegionFilterModel.direction = changeSort;
    } else {
      this.tableDetails[2].sortOptionList[sortBy] = changeSort;
      this.countyFilterModel.sortBy = sortBy;
      this.countyFilterModel.direction = changeSort;
    }

    this.getRegionSubregionAndCountyList(this.selectedTab);
  }

  getFilterDetails(type) {
    return type === 'Region' ? this.regionFilterDetails : type === 'Subregion' ? this.subRegionFilterDetails : this.countyFilterDetails;
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.storageService.set(this.viewRegionFilterSection, this.isRegionFilterDisplay);
      this.storageService.set(this.viewSubRegionFilterSection, this.isSubRegionFilterDisplay);
      this.storageService.set(this.viewCountyFilterSection, this.isCountyFilterDisplay);
      this.subscription.unsubscribe();
    }
  }
}

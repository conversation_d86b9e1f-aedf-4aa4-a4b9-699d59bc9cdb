<ng-container *ngIf="workTypes.controls.length && !noWorkTypeSelected; else noWorkType">
  <nb-card class="jha-work-steps-spinner appSpinner">
    <nb-card-header>
      <div class="row">
        <div class="col-12 col-sm-9 p-0 my-1">
          <div class="row m-0 w-100">
            <div class="col-6 col-md-4 d-flex align-items-center">
              <input
                nbInput
                pattern=".*\S.*"
                id="input-name"
                class="form-control"
                spellcheck="true"
                contenteditable="true"
                [ngModel]="workTypeName"
                fieldSize="large"
                readonly
              />
            </div>
            <div class="col-6 col-md-4 d-flex align-items-center">
              <ng-select
                bindLabel="workStep"
                notFoundText="Work Steps not found"
                placeholder="Add Work Step"
                [items]="masterWorkStepList"
                [clearable]="false"
                appendTo="body"
                class="w-100"
                [loading]="masterWorkStepListLoading"
                (change)="addWorkStepIntoList($event)"
                (open)="setWorkStepsList()"
              >
              </ng-select>
            </div>
          </div>
        </div>
        <div class="col-12 col-sm-3 my-1">
          <button
            class="linear-mode-button ms-sm-3 ms-0 float-sm-end"
            nbButton
            status="primary"
            size="small"
            type="button"
            (click)="addEditLotoWorkStep(null, 0, false)"
          >
            Add LOTO
          </button>
        </div>
      </div>
    </nb-card-header>
    <nb-card-body class="dropdownOverlap">
      <div class="form-control-group mt-1 row">
        <div class="col">
          <label class="label">Select relevant work steps for the on-site jobs.</label>
        </div>
      </div>
      <div class="form-control-group mt-1">
        <div id="fixed-table" class="table-responsive">
          <table class="table table-bordered" aria-describedby="Work Steps List">
            <thead>
              <tr>
                <th class="text-center col-1" id="rearrange">Rearrange</th>
                <th class="text-center col-1" id="include">Include</th>
                <th class="text-center col-9" id="workStep">Work Steps</th>
                <th class="text-center col-1" id="action">Action</th>
              </tr>
            </thead>
            <tbody cdkDropList (cdkDropListDropped)="dropWorkSteps($event)">
              <ng-container *ngIf="listOfWorkStep.controls.length; else noWorkStep">
                <ng-container *ngFor="let workStepItem of listOfWorkStep.controls; let i = index">
                  <tr cdkDrag cdkDragLockAxis="y" [formGroup]="workStepItem" class="dragHazardlist">
                    <td class="text-center jhaWorkStepTable col-1"><em class="fas fa-bars fa-1x"></em></td>
                    <td class="text-center jhaWorkStepTable col-1">
                      <nb-checkbox
                        class="chkPadding"
                        status="basic"
                        formControlName="isIncludedWorkStep"
                        (change)="workStepItem.get('isLotoWorkStep').value && onLotoWorkStepChange()"
                      ></nb-checkbox>
                    </td>
                    <td class="text-center jhaWorkStepTable col-9">
                      {{ workStepItem.get('workStep')?.value ? workStepItem.get('workStep')?.value : '-' }}
                    </td>
                    <td class="text-center jhaWorkStepTable col-1">
                      <ng-container *ngIf="workStepItem.get('isLotoWorkStep').value; else noLOTOWorkStep">
                        <em
                          class="fa fa-edit text-primary cursor-pointer"
                          nbTooltip="Edit"
                          nbTooltipPlacement="top"
                          nbTooltipStatus="primary"
                          (click)="addEditLotoWorkStep(workStepItem, i, true)"
                        ></em>
                      </ng-container>
                      <ng-template #noLOTOWorkStep>
                        <em
                          class="fa fa-trash text-primary cursor-pointer"
                          nbTooltip="Delete"
                          nbTooltipPlacement="top"
                          nbTooltipStatus="primary"
                          (click)="deleteWorkStepItem(workStepItem, i)"
                        ></em>
                      </ng-template>
                    </td>
                  </tr>
                </ng-container>
              </ng-container>
            </tbody>
          </table>
        </div>
      </div>
    </nb-card-body>
  </nb-card>
</ng-container>
<ng-template #noWorkType>
  <tr class="no-record text-center">
    <td colspan="12" class="text-center">
      <div class="mb-2">Please select work type first.</div>
    </td>
  </tr>
</ng-template>
<ng-template #noWorkStep>
  <tr class="no-record text-center">
    <td colspan="12" class="text-center">
      <div class="mb-2">No Work Steps Found</div>
    </td>
  </tr>
</ng-template>

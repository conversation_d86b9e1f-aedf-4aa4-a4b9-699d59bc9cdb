<ng-container *ngIf="workTypes.controls.length && !noWorkTypeSelected; else noWorkType">
  <nb-card class="jha-hazards-spinner appSpinner" [nbSpinner]="loading" nbSpinnerStatus="primary" nbSpinnerSize="large">
    <nb-card-header>
      <div class="row">
        <div class="col-12 col-sm-9 p-0 my-1">
          <div class="row m-0 w-100">
            <div class="col-6 col-md-4 d-flex align-items-center">
              <input
                nbInput
                pattern=".*\S.*"
                id="input-name"
                class="form-control"
                spellcheck="true"
                contenteditable="true"
                [ngModel]="workTypeName"
                fieldSize="large"
                readonly
              />
            </div>
            <div class="col-6 col-md-4 d-flex align-items-center">
              <ng-select
                #hazardSelect
                bindLabel="hazards"
                notFoundText="Hazards not found"
                placeholder="Add Hazard"
                [items]="masterHazardList"
                [clearable]="false"
                appendTo="body"
                class="w-100"
                [loading]="masterHazardListLoading"
                (change)="addHazardIntoList($event)"
                (open)="setHazardsList()"
              >
              </ng-select>
            </div>
          </div>
        </div>
      </div>
    </nb-card-header>

    <nb-card-body class="dropdownOverlap">
      <div class="form-control-group mt-1 row">
        <div class="col">
          <label class="label">Select relevant hazards for the on-site jobs.</label>
        </div>
      </div>
      <div class="form-control-group mt-1">
        <div id="fixed-table" class="table-responsive">
          <table class="table table-bordered" aria-describedby="Hazards List">
            <thead>
              <tr>
                <th class="text-center col-1" id="rearrange">Rearrange</th>
                <th class="text-center col-1" id="include">Include</th>
                <th class="text-center col-2" id="hazard">Hazard</th>
                <th class="text-center col-1" id="risk">Risk</th>
                <th class="text-center col-2" id="controlBarrier">Control Barriers</th>
                <th class="text-center col-2" id="protectiveBarrier">Protective Barriers</th>
                <th class="text-center col-2" id="supportBarrier">Support Barriers</th>
                <th class="text-center col-1" id="action">Action</th>
              </tr>
            </thead>
            <tbody cdkDropList (cdkDropListDropped)="dropHazards($event)">
              <ng-container *ngIf="listOfHazard.controls.length; else noHazards">
                <ng-container *ngFor="let hazardItem of listOfHazard.controls; let i = index">
                  <tr cdkDrag cdkDragLockAxis="y" [cdkDragDisabled]="viewdeletetedbutton" [formGroup]="hazardItem" class="dragHazardlist">
                    <td class="text-center jhaHazardstable col-1"><em class="fas fa-bars fa-1x"></em></td>
                    <td class="text-center jhaHazardstable col-1">
                      <nb-checkbox
                        class="chkPadding"
                        status="basic"
                        formControlName="isIncludedHazard"
                        [disabled]="hazardItem.get('isLotoHazard').value"
                      ></nb-checkbox>
                    </td>
                    <td class="text-center jhaHazardstable col-2">
                      {{ hazardItem.get('hazards')?.value ? hazardItem.get('hazards')?.value : '-' }}
                    </td>
                    <td class="text-center jhaHazardstable col-1">
                      {{ hazardItem.get('riskLevel')?.value ? hazardItem.get('riskLevel').value : '-' }}
                    </td>
                    <td class="text-center jhaHazardstable col-2">
                      {{ hazardItem.get('controlBarriers')?.value ? hazardItem.get('controlBarriers')?.value : '-' }}
                    </td>
                    <td class="text-center jhaHazardstable col-3">
                      {{ hazardItem.get('protectiveBarriers')?.value ? hazardItem.get('protectiveBarriers')?.value : '-' }}
                    </td>
                    <td class="text-center jhaHazardstable col-2">
                      {{ hazardItem.get('supportBarriers')?.value ? hazardItem.get('supportBarriers')?.value : '-' }}
                    </td>
                    <td class="text-center jhaHazardstable col-1">
                      <ng-container *ngIf="!hazardItem.get('isLotoHazard').value">
                        <em
                          class="fa fa-trash text-primary cursor-pointer"
                          nbTooltip="Delete"
                          nbTooltipPlacement="top"
                          nbTooltipStatus="primary"
                          (click)="deleteHazardItem(hazardItem, i)"
                        ></em>
                      </ng-container>
                    </td>
                  </tr>
                </ng-container>
              </ng-container>
            </tbody>
          </table>
        </div>
      </div>
    </nb-card-body>
  </nb-card>
</ng-container>
<ng-template #noWorkType>
  <tr class="no-record text-center">
    <td colspan="12" class="text-center">
      <div class="mb-2">Please select work type first.</div>
    </td>
  </tr>
</ng-template>
<ng-template #noHazards>
  <tr class="no-record text-center">
    <td colspan="12" class="text-center">
      <div class="mb-2">No Hazards Found</div>
    </td>
  </tr>
</ng-template>

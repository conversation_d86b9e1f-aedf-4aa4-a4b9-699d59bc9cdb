import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { LotoAddEditComponent } from './loto-add-edit/loto-add-edit.component';
import { LotoBarrierInfoComponent } from './loto-barrier-info/loto-barrier-info.component';

const routes: Routes = [
  {
    path: '',
    component: LotoBarrierInfoComponent,
    data: { pageTitle: 'LOTO' }
  },
  {
    path: 'add',
    component: LotoAddEditComponent,
    data: { pageTitle: 'Add LOTO' }
  },
  {
    path: ':mode/:id',
    component: LotoAddEditComponent,
    data: { pageTitle: 'Update LOTO' }
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class LotoRoutingModule {}

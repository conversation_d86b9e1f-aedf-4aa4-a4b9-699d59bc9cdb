import { Component, Input, OnD<PERSON>roy, OnInit } from '@angular/core';
import { HazardContact } from '../../../settings/settings.model';
import { FormArray, FormBuilder, FormControl, FormGroup, FormGroupDirective } from '@angular/forms';
import { Subscription } from 'rxjs';
import { JhaUploadService } from '../jha-upload.service';
import { WorkTypeService } from '../../../settings/work-type/work-type.service';
import { AlertService } from '../../../../../@shared/services';
import { HazardService } from '../../../settings/hazard/hazard.service';
import { CdkDragDrop, moveItemInArray } from '@angular/cdk/drag-drop';
import { HazardDetailByWorkType, WorkStepsForHazards, WorkTypes } from '../../../../../@shared/models/jha.model';
import { map } from 'rxjs/operators';
import { JhaHelperService } from '../../jha-helper.service';

@Component({
  selector: 'sfl-jha-hazards',
  templateUrl: './jha-hazards.component.html',
  styleUrls: ['./jha-hazards.component.scss']
})
export class JhaHazardsComponent implements OnInit, OnDestroy {
  @Input() workTypesForm: string;
  @Input() workStepsForm: string;
  @Input() hazardsForm: string;
  @Input() lotoBarrierForm: string;
  private subscription: Subscription = new Subscription();

  masterHazardListLoading = false;
  noWorkTypeSelected = false;
  masterHazardList = [];
  workTypes: FormArray;
  listOfWorkStep: FormArray;
  listOfHazard: FormArray;
  lotoBarrier: FormControl;

  constructor(
    private fb: FormBuilder,
    private rootFormGroup: FormGroupDirective,
    private readonly hazardService: HazardService,
    private readonly jhaHelperService: JhaHelperService
  ) {}

  ngOnInit(): void {
    this.workTypes = this.rootFormGroup.control.get(this.workTypesForm) as FormArray;
    this.listOfWorkStep = this.rootFormGroup.control.get(this.workStepsForm) as FormArray;
    this.listOfHazard = this.rootFormGroup.control.get(this.hazardsForm) as FormArray;
    this.lotoBarrier = this.rootFormGroup.control.get(this.lotoBarrierForm) as FormControl;
    this.getAllActiveHazardList();
    this.setOrderOfListOfHazardItems();
  }

  get workTypeName(): string {
    return this.workTypes.value.map((item: WorkTypes) => item.workTypeName).join(', ');
  }

  setOrderOfListOfHazardItems(): void {
    let i = 1;
    this.listOfHazard.controls.forEach((item: FormGroup, index: number) => {
      item.get('hazardOrder')?.setValue(i++);
      if (index === 0) {
        item.get('orderChange')?.setValue(true);
      }
    });
  }

  dropHazards(event: CdkDragDrop<string[]>): void {
    moveItemInArray(this.listOfHazard.controls, event.previousIndex, event.currentIndex);
    this.setOrderOfListOfHazardItems();
  }

  deleteHazardItem(hazardItem: FormGroup, index: number): void {
    this.listOfHazard.removeAt(index);
    this.setOrderOfListOfHazardItems();
  }

  addHazardIntoList(hazardItem: HazardDetailByWorkType): void {
    const formGroup = this.jhaHelperService.initOrPatchListOfHazards(hazardItem);
    this.listOfHazard.push(formGroup);
  }

  setHazardsList(): void {
    const availableHazardsInList = this.listOfHazard.value.map((item: HazardDetailByWorkType) => item.hazardId);
    this.masterHazardList = this.masterHazardList.map(item => ({
      ...item,
      disabled: availableHazardsInList.includes(item.hazardId)
    }));
  }

  getAllActiveHazardList(): void {
    const workTypes = this.workTypes.value;
    const hasWorkTypeIds = workTypes.filter(x => x.workTypeId).length > 0 ? true : false;

    if (hasWorkTypeIds) {
      this.masterHazardListLoading = true;
      this.subscription.add(
        this.hazardService.getHazard().subscribe({
          next: (res: any) => {
            this.masterHazardList = res.map(
              (item: HazardContact) =>
                new HazardDetailByWorkType(
                  item.id,
                  item.name,
                  item.riskLevelStr,
                  item.riskLevel,
                  item.controlBarriers,
                  item.protectiveBarriers,
                  item.supportBarriers
                )
            );
            this.masterHazardListLoading = false;
          },
          error: _e => {
            this.masterHazardListLoading = false;
          }
        })
      );
    } else {
      this.noWorkTypeSelected = true;
    }
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }
}

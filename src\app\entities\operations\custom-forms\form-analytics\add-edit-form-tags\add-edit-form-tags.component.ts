import { Component, Input, OnD<PERSON>roy, OnInit, ViewEncapsulation } from '@angular/core';
import { BsModalRef } from 'ngx-bootstrap/modal';
import { Subject, Subscription } from 'rxjs';
import { AlertService } from '../../../../../@shared/services';
import { CustomFormService } from '../../custom-form.service';
import { TagFormModel } from '../../custom-forms.model';

@Component({
  selector: 'sfl-add-edit-form-tags',
  templateUrl: './add-edit-form-tags.component.html',
  styleUrls: ['./add-edit-form-tags.component.scss'],
  encapsulation: ViewEncapsulation?.None
})
export class AddEditFormTagsComponent implements OnInit, OnDestroy {
  public onClose: Subject<boolean>;
  subscription: Subscription = new Subscription();
  @Input() isEdit: boolean;
  @Input() editSlug: number;
  recordDetails: any;
  tagFormModel: TagFormModel = new TagFormModel();
  loading = false;
  tagTypeList: any[] = [];
  controlTypeList: any[] = [];

  constructor(
    public readonly customFormService: CustomFormService,
    private readonly alertService: AlertService,
    public _bsModalRef: BsModalRef
  ) {}

  ngOnInit(): void {
    this.onClose = new Subject();
    this.getControlTypeList();
    if (this.isEdit && this.editSlug) {
      this.getDetailsByTagId();
    }
  }

  getDetailsByTagId() {
    this.loading = true;
    this.customFormService.getTagDetailsById(this.editSlug).subscribe(
      response => {
        this.getTagDataTypeList(response.controlType, success => {
          if (success) {
            this.tagFormModel = response;
          }
          this.loading = false;
        });
      },
      () => (this.loading = false)
    );
  }

  getControlTypeList() {
    this.loading = true;
    this.subscription.add(
      this.customFormService.getControlTypeList().subscribe({
        next: response => {
          this.loading = false;
          this.controlTypeList = response;
        },
        error: e => {
          this.loading = false;
        }
      })
    );
  }

  getTagDataTypeList(controlTypeId = null, callback = null) {
    this.tagFormModel.controlDataType = null;
    this.tagTypeList = [];
    this.loading = true;
    const controlType = controlTypeId ? controlTypeId : this.tagFormModel.controlType;
    this.subscription.add(
      this.customFormService.getTagTypeList(controlType).subscribe({
        next: response => {
          this.loading = false;
          this.tagTypeList = response;
          if (callback) {
            callback(true);
          }
        },
        error: () => {
          this.loading = false;
        }
      })
    );
  }

  onSubmit() {
    this.subscription.add(
      this.customFormService.createUpdateAnalyticsTag(this.tagFormModel).subscribe({
        next: () => {
          this.loading = false;
          this.alertService.showSuccessToast(`Tag ${this.isEdit ? 'updated' : 'saved'} successfully.`);
          this.onClose.next(true);
          this._bsModalRef.hide();
        },
        error: e => {
          this.loading = false;
        }
      })
    );
  }

  onCancel(): void {
    this.onClose.next(false);
    this._bsModalRef.hide();
  }

  ngOnDestroy() {
    this.subscription.unsubscribe();
    this._bsModalRef.hide();
  }
}

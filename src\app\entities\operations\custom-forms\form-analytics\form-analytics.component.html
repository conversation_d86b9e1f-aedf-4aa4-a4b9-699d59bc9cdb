<nb-card class="customerSpinner appSpinner" [nbSpinner]="loading" nbSpinnerStatus="primary" nbSpinnerSize="large">
  <nb-card-header>
    <div class="row">
      <div class="col-12 d-flex align-items-center"></div>
    </div>
    <div class="row">
      <div class="col-12 d-flex justify-content-between align-items-center">
        <h6>Form Analytics</h6>
        <div>
          <button
            class="linear-mode-button me-2"
            nbButton
            status="primary"
            size="small"
            [disabled]="loading"
            (click)="openCreateEditModal(false, null)"
            type="button"
            *ngIf="userRole === 'admin' || userRole === 'commercialAssetsManager' || userRole === 'fieldTech'"
          >
            <span class="d-none d-md-inline-block">Add Tag</span>
            <em class="d-inline-block d-md-none fa-regular fa-square-plus"></em>
          </button>
          <button
            class="linear-mode-button"
            nbButton
            status="basic"
            size="small"
            [disabled]="loading"
            (click)="goToLandingPage()"
            type="button"
          >
            <span class="d-none d-lg-inline-block">Back</span>
            <i class="d-inline-block d-lg-none fa-solid fa-arrow-left"></i>
          </button>
        </div>
      </div>
    </div>
  </nb-card-header>
  <nb-card-body>
    <div class="row">
      <div class="col-12 customerFilter appFilter">
        <sfl-filter
          [filterDetails]="filterDetails"
          (refreshList)="refreshList($event)"
          (refreshTableHeight)="this.isFilterDisplay = $event"
        ></sfl-filter>
      </div>
      <div id="fixed-table" setTableHeight [isFilterDisplay]="isFilterDisplay" class="col-12 table-responsive mt-3 table-card-view">
        <table class="table table-hover table-bordered" aria-describedby="Customer List">
          <thead>
            <tr>
              <th (click)="sort('tagName', sortOptionList['tagName'])" id="tagName">
                <div class="d-flex align-items-center">
                  Tag Name
                  <span
                    class="fa text-center cursor-pointer ms-auto"
                    [ngClass]="{
                      'fa-arrow-up': sortOptionList['tagName'] === 'desc',
                      'fa-arrow-down': sortOptionList['tagName'] === 'asc',
                      'icon-selected': filterModel.sortBy === 'tagName'
                    }"
                  ></span>
                </div>
              </th>
              <th (click)="sort('controlTypeName', sortOptionList['controlTypeName'])" id="controlTypeName">
                <div class="d-flex align-items-center">
                  Control Type
                  <span
                    class="fa text-center cursor-pointer ms-auto"
                    [ngClass]="{
                      'fa-arrow-up': sortOptionList['controlTypeName'] === 'desc',
                      'fa-arrow-down': sortOptionList['controlTypeName'] === 'asc',
                      'icon-selected': filterModel.sortBy === 'controlTypeName'
                    }"
                  ></span>
                </div>
              </th>
              <th (click)="sort('controlDateTypeName', sortOptionList['controlDateTypeName'])" id="controlDateTypeName">
                <div class="d-flex align-items-center">
                  Tag Type
                  <span
                    class="fa text-center cursor-pointer ms-auto"
                    [ngClass]="{
                      'fa-arrow-up': sortOptionList['controlDateTypeName'] === 'desc',
                      'fa-arrow-down': sortOptionList['controlDateTypeName'] === 'asc',
                      'icon-selected': filterModel.sortBy === 'controlDateTypeName'
                    }"
                  ></span>
                </div>
              </th>
              <th class="text-center" id="Status">Status</th>
              <th class="text-center min-width-action" id="action">Action</th>
            </tr>
          </thead>
          <tbody>
            <tr
              *ngFor="
                let analytics of analyticsListingData
                  | paginate : { itemsPerPage: filterModel.itemsCount, currentPage: currentPage, totalItems: totalCount }
              "
            >
              <td data-title="Tag Name">{{ analytics.tagName }}</td>
              <td data-title="Control Type">
                <span
                  *ngIf="analytics.controlTypeName"
                  nbTooltip="{{ analytics?.controlTypeName }}"
                  nbTooltipPlacement="top"
                  nbTooltipStatus="primary"
                >
                  <sfl-read-more [content]="analytics?.controlTypeName"></sfl-read-more>
                </span>
              </td>
              <td data-title="Tag Type">
                <span
                  *ngIf="analytics.controlDateTypeName"
                  nbTooltip="{{ analytics?.controlDateTypeName }}"
                  nbTooltipPlacement="top"
                  nbTooltipStatus="primary"
                >
                  <sfl-read-more [content]="analytics?.controlDateTypeName"></sfl-read-more>
                </span>
              </td>
              <td data-title="Status" class="text-center">
                <nb-toggle
                  status="primary"
                  [(checked)]="analytics.isActive"
                  (checkedChange)="changeAnalyticsStatus(analytics?.qestDataAnalyticsId)"
                  *ngIf="userRole === 'admin' || userRole === 'commercialAssetsManager' || userRole === 'fieldTech'; else notAdminAndCam"
                ></nb-toggle>
                <ng-template #notAdminAndCam>
                  {{ analytics.isActive ? 'Active' : 'Inactive' }}
                </ng-template>
              </td>
              <td data-title="Action" class="text-center customer-action">
                <div>
                  <em
                    class="fa fa-edit cursor-pointer text-primary me-2"
                    nbTooltip="Edit"
                    nbTooltipPlacement="top"
                    nbTooltipStatus="primary"
                    aria-hidden="true"
                    (click)="openCreateEditModal(true, analytics.qestDataAnalyticsId)"
                    *ngIf="userRole === 'admin' || userRole === 'commercialAssetsManager' || userRole === 'fieldTech'"
                  ></em>
                  <em
                    class="fa fa-trash cursor-pointer text-danger"
                    nbTooltip="Delete"
                    nbTooltipPlacement="top"
                    nbTooltipStatus="primary"
                    aria-hidden="true"
                    (click)="onAnalyticsDelete(analytics.qestDataAnalyticsId)"
                    *ngIf="userRole === 'admin' || userRole === 'commercialAssetsManager' || userRole === 'fieldTech'"
                  ></em>
                </div>
              </td>
            </tr>
            <tr>
              <td colspan="9" *ngIf="!analyticsListingData?.length" class="no-record text-center">No Data Found</td>
            </tr>
          </tbody>
        </table>
      </div>
      <div class="mt-2 d-md-flex align-items-center" *ngIf="analyticsListingData?.length">
        <div class="d-flex align-items-center">
          <label class="mb-0">Items per page: </label>
          <ng-select class="ms-2" [(ngModel)]="pageSize" [clearable]="false" [searchable]="false" (change)="onChangeSize()" appendTo="body">
            <ng-option value="5">5</ng-option>
            <ng-option value="10">10</ng-option>
            <ng-option value="50">50</ng-option>
            <ng-option value="100">100</ng-option>
          </ng-select>
        </div>
        <strong class="ms-md-3">Total: {{ totalCount }}</strong>
        <div class="ms-md-auto ms-sm-0">
          <pagination-controls (pageChange)="onPageChange($event)" class="paginate"></pagination-controls>
        </div>
      </div>
    </div>
  </nb-card-body>
</nb-card>

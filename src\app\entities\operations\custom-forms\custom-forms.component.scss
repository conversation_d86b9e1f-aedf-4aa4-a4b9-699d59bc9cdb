.row {
  height: 100%;
}

.custom-row-height {
  display: flex;
  flex-direction: column;
  height: 100%;
}
::ng-deep .contenttt {
  background-color: red;
  flex: 1;
}

.ul-without-bullets {
  position: relative;
  list-style-type: none;
  padding: 0;
  margin: 0 !important;
  height: 100vh;
  // height: calc(100vh - 221px);
  .sidebar-options {
    font-size: 12px;
    border: 1px solid #151a2f;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    cursor: move;
    text-align: center;
    padding: 5px;
    width: 100%;
    position: relative;

    &.sidebar-option-dragged {
      background-color: #add8e6;
      border: 1px dashed black;
      opacity: 0.5;
      color: black;
    }
  }
  .draggable-div {
    position: absolute;
    top: 0px;
    left: 0px;
    bottom: 0px;
    right: 0px;
    cursor: move;
    z-index: 99999;
    width: 100%;

    &.on-element-dragged {
      background-color: #add8e6;
      // border: 1px dashed black;
      opacity: 0.5;
      max-height: 25px;
      width: 100%;
    }
  }

  .help-div {
    position: absolute;
    bottom: 0;
    background-color: #3968fa !important;
  }
}

::ng-deep canvas,
::ng-deep .canvas-container {
  width: 100% !important;
}

// ::ng-deep .lower-canvas {
//   z-index: 5;
// }

// ::ng-deep .upper-canvas {
//   z-index: 3;
// }
.cdk-drag-placeholder {
  opacity: 0;
}

.canvas-display-div {
  background-color: #151a2f;
  height: 100vh;
  overflow-y: auto;
}

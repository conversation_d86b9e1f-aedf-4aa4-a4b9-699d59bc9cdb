export class GeneralInfo {
  public emergencyNumber: number;
  public qeSafetyDireactor: string;
  public qeSafetyNumber: number;
  public typicalPEE: string;
  public id: number;
}

export class WorkStep {
  public id: number;
  public workStepName: string;
  public workSteps: string;
  public isActive: boolean;
  public listHazards: HazardContact[] = [];
  public listHazard: HazardContact[] = [];
}

export class WorkStepList {
  public id: number;
  public isActive: boolean;
  public workStepName: string;
  public workStepId: number;
  public listHazards: HazardsList[] = [];
}
export class HazardsList {
  public workStepId: number;
  public isActive: boolean;
  public hazardsCounts: number;
  public workStepName: string;
  public workSteps?: string;
  public controlBarriers: string;
  public hazards: string;
  public protectiveBarriers: string;
  public riskLevel: string;
  public supportBarriers: string;
  public hazardsCount?: number;
  public name: string;
  public riskLevelStr: string;
}
export class HazardContact {
  public id: number;
  public hazardsId: number;
  public name: string;
  public riskLevelStr: string;
  public riskLevel: number;
  public controlBarriers: string;
  public supportBarriers: string;
  public protectiveBarriers: string;
  public isDeleted: boolean;
  public hazards: string;
  public workSteps: string;
}

export class LOTOBarrier {
  public id: number = 0;
  public name: string;
  public riskLevel: number;
  public controlBarrier: string;
  public supportBarrier: string;
  public protectiveBarrier: string;
  public riskLevelStr: string;

  constructor(params: Partial<LOTOBarrier>) {
    Object.assign(this, params);
  }
}

export class LOTO {
  public id: number = 0;
  public name: string;
  public equipmentType: string;
  public equipmentModel: string;
  public asBuildPageNumber: number;

  constructor(
    id: number = 0,
    name: string = '',
    equipmentType: string = '',
    equipmentModel: string = '',
    asBuildPageNumber: number = null
  ) {
    this.equipmentType = equipmentType;
    this.equipmentModel = equipmentModel;
    this.asBuildPageNumber = asBuildPageNumber;
    this.name = name ?? `LOTO - (${this.equipmentType}) - ${this.equipmentModel} - ${this.asBuildPageNumber}`;
    this.id = id ?? 0;
  }
}

export class LOTOItemEmitResponse {
  public lotoWorkStepItem: LOTO;
  public lotoHazardItem: LOTOBarrier;

  constructor(lotoWorkStepItem: LOTO, lotoHazardItem: LOTOBarrier) {
    this.lotoWorkStepItem = lotoWorkStepItem;
    this.lotoHazardItem = lotoHazardItem;
  }
}

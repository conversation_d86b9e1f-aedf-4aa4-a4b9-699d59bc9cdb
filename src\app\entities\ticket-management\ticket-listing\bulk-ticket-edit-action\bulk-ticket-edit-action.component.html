<nb-card class="ticketSpinner appSpinner" [nbSpinner]="loading" nbSpinnerStatus="primary" nbSpinnerSize="large">
  <nb-card-header>
    <div class="row">
      <div class="col-12 d-flex align-items-center">
        <h6>Bulk Edit Tickets</h6>
        <div class="ms-auto">
          <button type="button" class="close" aria-label="Close" (click)="closeModel()">
            <span aria-hidden="true"><em class="fa-solid fa-xmark fa-xl"></em></span>
          </button>
        </div>
      </div>
    </div>
  </nb-card-header>
  <nb-card-body class="dropdownOverlap">
    <div class="row">
      <div class="col-12 my-3 d-flex align-items-center">
        <div class="me-2 mt-1" *ngIf="currentStep === 1">
          <nb-checkbox
            id="select-all"
            class="sfl-track-checkbox"
            [(ngModel)]="isMasterSel"
            (change)="selectDeselectAll()"
            name="selectAllFiles"
          >
          </nb-checkbox>
        </div>
        <h5 class="mb-0" *ngIf="currentStep === 1">Select Tickets</h5>
        <h5 class="mb-0" *ngIf="currentStep === 2">Select Fields</h5>
        <h5 class="mb-0" *ngIf="currentStep === 3">Send Email(S)?</h5>
        <h5 class="mb-0" *ngIf="currentStep === 4">Confirmation</h5>
        <button
          class="linear-mode-button ms-2 button-h-100"
          nbButton
          status="primary"
          size="small"
          type="button"
          (click)="SaveBulkAction()"
          [disabled]="!selectedBulkEditTickets?.length"
          *ngIf="currentStep === 4"
        >
          Confirm And Update
        </button>
        <button
          class="linear-mode-button ms-2 button-h-100"
          nbButton
          status="primary"
          size="small"
          type="button"
          (click)="proceedNextStep(bulkEditForm)"
          [disabled]="currentStep === 1 ? !selectedBulkEditTickets?.length : currentStep === 2 ? !isAnyFiledAdded() : false"
          *ngIf="currentStep !== 4 && currentStep !== 5"
        >
          Next
        </button>
        <button
          class="linear-mode-button ms-2 button-h-100"
          nbButton
          status="primary"
          size="small"
          type="button"
          (click)="proceedBackStep()"
          [disabled]="loading"
          *ngIf="currentStep !== 5"
        >
          Back
        </button>
        <button
          *ngIf="currentStep !== 5"
          class="linear-mode-button ms-2 button-h-100"
          nbButton
          size="small"
          type="button"
          (click)="closeModel()"
          [disabled]="loading"
        >
          Cancel
        </button>
      </div>
      <div id="fixed-table" setTableHeight class="col-12 table-responsive table-card-view" *ngIf="currentStep === 1">
        <table class="table table-hover table-bordered" aria-describedby="Ticket List">
          <thead>
            <tr>
              <th scope="col" id="select-all" class="text-center"></th>
              <th scope="col" (click)="sort('Priority', sortOptionList['Priority'])">
                <div class="d-flex justify-content-center align-items-center">
                  Priority
                  <span
                    class="fa cursor-pointer ms-2"
                    [ngClass]="{
                      'fa-arrow-up': sortOptionList['Priority'] === 'desc',
                      'fa-arrow-down': sortOptionList['Priority'] === 'asc',
                      'icon-selected': filterModel.sortBy === 'Priority'
                    }"
                  ></span>
                </div>
              </th>
              <th scope="col" (click)="sort('Number', sortOptionList['Number'])">
                <div class="d-flex align-items-center">
                  Number
                  <span
                    class="fa cursor-pointer ms-auto"
                    [ngClass]="{
                      'fa-arrow-up': sortOptionList['Number'] === 'desc',
                      'fa-arrow-down': sortOptionList['Number'] === 'asc',
                      'icon-selected': filterModel.sortBy === 'Number'
                    }"
                  ></span>
                </div>
              </th>
              <th scope="col" (click)="sort('CustomerPortfolio', sortOptionList['CustomerPortfolio'])">
                <div class="d-flex align-items-center">
                  <span class="me-2">Customer (Portfolio)</span>
                  <span
                    class="fa cursor-pointer ms-auto"
                    [ngClass]="{
                      'fa-arrow-up': sortOptionList['CustomerPortfolio'] === 'desc',
                      'fa-arrow-down': sortOptionList['CustomerPortfolio'] === 'asc',
                      'icon-selected': filterModel.sortBy === 'CustomerPortfolio'
                    }"
                  ></span>
                </div>
              </th>
              <th scope="col" (click)="sort('Site', sortOptionList['Site'])">
                <div class="d-flex align-items-center">
                  <span class="me-2">Site</span>
                  <span
                    class="fa cursor-pointer ms-auto"
                    [ngClass]="{
                      'fa-arrow-up': sortOptionList['Site'] === 'desc',
                      'fa-arrow-down': sortOptionList['Site'] === 'asc',
                      'icon-selected': filterModel.sortBy === 'Site'
                    }"
                  ></span>
                </div>
              </th>
              <th scope="col" (click)="sort('Device', sortOptionList['Device'])">
                <div class="d-flex align-items-center">
                  <span class="me-2">Device</span>
                  <span
                    class="fa cursor-pointer ms-auto"
                    [ngClass]="{
                      'fa-arrow-up': sortOptionList['Device'] === 'desc',
                      'fa-arrow-down': sortOptionList['Device'] === 'asc',
                      'icon-selected': filterModel.sortBy === 'Device'
                    }"
                  ></span>
                </div>
              </th>
              <th scope="col" class="mw-150">Issue</th>
              <th scope="col" class="mw-150">Recent Activity</th>
              <th (click)="sort('Open', sortOptionList['Open'])" id="date">
                <div class="d-flex align-items-center">
                  <span class="me-2">Opened</span>
                  <span
                    class="fa cursor-pointer ms-auto"
                    [ngClass]="{
                      'fa-arrow-up': sortOptionList['Open'] === 'desc',
                      'fa-arrow-down': sortOptionList['Open'] === 'asc',
                      'icon-selected': filterModel.sortBy === 'Open'
                    }"
                  ></span>
                </div>
              </th>
              <th (click)="sort('Close', sortOptionList['Close'])" id="date">
                <div class="d-flex align-items-center">
                  <span class="me-2">Closed</span>
                  <span
                    class="fa cursor-pointer ms-auto"
                    [ngClass]="{
                      'fa-arrow-up': sortOptionList['Close'] === 'desc',
                      'fa-arrow-down': sortOptionList['Close'] === 'asc',
                      'icon-selected': filterModel.sortBy === 'Close'
                    }"
                  ></span>
                </div>
              </th>
              <ng-container *ngIf="userRole[0] !== 'customer'">
                <th (click)="sort('RegionName', sortOptionList['RegionName'])" id="date">
                  <div class="d-flex align-items-center">
                    <span class="me-2">Region</span>
                    <span
                      class="fa cursor-pointer ms-auto"
                      [ngClass]="{
                        'fa-arrow-up': sortOptionList['RegionName'] === 'desc',
                        'fa-arrow-down': sortOptionList['RegionName'] === 'asc',
                        'icon-selected': filterModel.sortBy === 'RegionName'
                      }"
                    ></span>
                  </div>
                </th>
                <th (click)="sort('SubRegionName', sortOptionList['SubRegionName'])" id="date">
                  <div class="d-flex align-items-center">
                    <span class="me-2">Subregion</span>
                    <span
                      class="fa cursor-pointer ms-auto"
                      [ngClass]="{
                        'fa-arrow-up': sortOptionList['SubRegionName'] === 'desc',
                        'fa-arrow-down': sortOptionList['SubRegionName'] === 'asc',
                        'icon-selected': filterModel.sortBy === 'SubRegionName'
                      }"
                    ></span>
                  </div>
                </th>
              </ng-container>
              <th class="text-end" scope="col">Truck Roll Count</th>
              <th scope="col">Status</th>
            </tr>
          </thead>
          <tbody>
            <ng-container *ngIf="tickets?.length">
              <tr
                *ngFor="
                  let item of tickets
                    | paginate : { id: 'ticketBulkEdit', itemsPerPage: pageSize, currentPage: currentPage, totalItems: total }
                "
              >
                <td data-title="Select Ticket" class="text-center">
                  <nb-checkbox
                    id="select-file+{{ item.id }}"
                    class="sfl-track-checkbox"
                    name="selectSingleTicket+{{ item.id }}"
                    (change)="singleTicketCheckChanged(item)"
                    [(ngModel)]="item.isSelected"
                    [checked]="item.isSelected"
                  >
                  </nb-checkbox>
                </td>
                <td data-title="Priority" class="text-center">
                  <img
                    *ngIf="item?.priorityStr === 'High'"
                    nbTooltip="High"
                    nbTooltipStatus="primary"
                    src="assets/images/High.svg"
                    alt="High"
                  />
                  <img
                    alt="Medium"
                    *ngIf="item?.priorityStr === 'Medium'"
                    nbTooltip="Medium"
                    nbTooltipStatus="primary"
                    src="assets/images/Medium.svg"
                  />
                  <img
                    alt="Low"
                    *ngIf="item?.priorityStr === 'Low'"
                    nbTooltip="Low"
                    nbTooltipStatus="primary"
                    src="assets/images/Low.svg"
                  />
                  <img
                    alt="Safety"
                    *ngIf="item?.priorityStr === 'Safety'"
                    nbTooltip="Safety"
                    nbTooltipStatus="primary"
                    src="assets/images/Safety.svg"
                  />
                </td>
                <td data-title="Number" class="pointerTicketNumberLink">
                  <a
                    [href]="'../entities/ticket/detail/view/' + item.ticketNumber"
                    [contextMenu]="actionMenu"
                    [contextMenuValue]="{ data: item.ticketNumber }"
                  >
                    {{ item?.ticketNumber }}
                  </a>
                </td>
                <td data-title="Customer (Portfolio)">{{ item?.customerPortfolio }}</td>
                <td data-title="Site">{{ item?.siteName }}</td>
                <td data-title="Device">
                  <div>
                    <ng-container
                      *ngFor="let device of item.ticketDevices | slice : 0 : (item.show ? item.ticketDevices?.length : 5); last as last"
                    >
                      <span
                        [ngStyle]="{
                          color: device?.alertStatus === 'Ticketed' ? '#f0c108' : device?.alertStatus === 'Resolved' ? '#61c98b' : ''
                        }"
                        >{{ device?.deviceName }}</span
                      >{{ last ? '' : ', ' }}
                    </ng-container>
                    <a *ngIf="item.ticketDevices?.length > 5" class="text-primary link cursor-pointer" (click)="item.show = !item.show">
                      {{ item.show ? '  ' + ' ...- ' : '  ' + ' ...+ ' }}</a
                    >
                  </div>
                </td>
                <td data-title="Issue">
                  <div *ngIf="item?.issue" nbTooltip="{{ item?.issue }}" nbTooltipPlacement="top" nbTooltipStatus="primary">
                    <sfl-read-more [content]="item?.issue"></sfl-read-more>
                  </div>
                </td>
                <td data-title="Recent Activity">
                  <span
                    *ngIf="item?.activityLog"
                    nbTooltip="{{ item?.activityDate | date : fullDateFormat }} : {{ item?.activityLog }}"
                    nbTooltipPlacement="top"
                    nbTooltipStatus="primary"
                  >
                    <sfl-read-more [content]="(item?.activityDate | date : fullDateFormat + ' : ') + item?.activityLog"></sfl-read-more>
                  </span>
                </td>
                <td data-title="Opened">
                  {{ item?.open | date : fullDateFormat }}
                </td>
                <td data-title="Closed">
                  {{ item?.close | date : fullDateFormat }}
                </td>
                <ng-container *ngIf="userRole[0] !== 'customer'">
                  <td data-title="Region">{{ item?.regionName }}</td>
                  <td data-title="Subregion">{{ item?.subRegionName }}</td>
                </ng-container>
                <td data-title="Truck Roll Count" class="text-end">
                  <a
                    [ngClass]="{ 'custom-link': item?.truckRollNumbers?.length }"
                    (click)="item?.truckRollNumbers?.length && openTruckRollModal(item, truckRollModal)"
                  >
                    <span>{{ item?.truckRoll }} </span>
                  </a>
                </td>
                <td
                  data-title="Status"
                  [ngStyle]="item.isResolve === true ? { 'background-color': 'green' } : { 'background-color': '' }"
                  class="text-center"
                >
                  {{ item?.statusStr }}
                </td>
              </tr>
            </ng-container>
            <ng-container *ngIf="!tickets?.length">
              <tr>
                <td colspan="13" class="no-record text-center">No Data Found</td>
              </tr>
            </ng-container>
          </tbody>
        </table>
      </div>
      <div class="mt-2 d-md-flex align-items-center" *ngIf="tickets?.length && currentStep === 1">
        <div class="d-flex align-items-center">
          <label class="mb-0">Items per page: </label>
          <ng-select
            class="ms-2"
            id="ticketBulkEdit"
            [(ngModel)]="pageSize"
            [clearable]="false"
            [searchable]="false"
            (change)="currentPage = 0"
          >
            <ng-option value="10">10</ng-option>
            <ng-option value="50">50</ng-option>
            <ng-option value="100">100</ng-option>
          </ng-select>
        </div>
        <strong class="ms-md-3">Total: {{ total }}</strong>
        <div class="ms-md-auto ms-sm-0">
          <pagination-controls id="ticketBulkEdit" (pageChange)="onPageChange($event)" class="paginate"></pagination-controls>
        </div>
      </div>
    </div>
    <div class="mt-3 bulk-edit-form">
      <form
        name="bulkEditForm"
        #bulkEditForm="ngForm"
        aria-labelledby="title"
        autocomplete="off"
        (ngSubmit)="bulkEditForm?.form?.valid && proceedNextStep(bulkEditForm)"
      >
        <div *ngIf="currentStep === 2">
          <!-- Ticket Status -->
          <div class="row my-2 align-items-center justify-content-start">
            <div class="col-md-2">
              <nb-checkbox
                status="basic"
                name="ticketStatusSelection"
                id="ticketStatusSelection"
                #ticketStatusSelection="ngModel"
                class="sfl-track-checkbox"
                [checked]="availableFields.ticketStatus"
                [(ngModel)]="availableFields.ticketStatus"
                (change)="addRemoveBulkEditFields($event.target.checked, 1)"
              >
                Ticket Status
              </nb-checkbox>
            </div>
            <div class="col-md-10 d-flex align-items-center justify-content-start">
              <div>
                <nb-radio-group
                  class="d-flex"
                  name="ticketStatus"
                  #ticketStatus="ngModel"
                  [(ngModel)]="ticketEditForm.ticketStatus"
                  [required]="availableFields.ticketStatus"
                  [disabled]="!availableFields.ticketStatus"
                >
                  <nb-radio [value]="1">Open</nb-radio>
                  <nb-radio [value]="2">Pending</nb-radio>
                  <nb-radio [value]="3">Closed</nb-radio>
                </nb-radio-group>
                <div class="error-message-section">
                  <sfl-error-msg
                    [control]="ticketStatus"
                    [isFormSubmitted]="bulkEditForm?.submitted"
                    fieldName="Ticket Status"
                  ></sfl-error-msg>
                </div>
              </div>
              <div *ngIf="ticketEditForm.ticketStatus === 3 && availableFields.ticketStatus">
                <label class="label" for="closedDate"> Closed <span class="ms-1 text-danger">*</span> </label>
                <div>
                  <input
                    nbInput
                    name="closedDate"
                    placeholder="Select Closed Date"
                    fullWidth
                    [nbDatepicker]="closedDatePicker"
                    #closedDate="ngModel"
                    [(ngModel)]="ticketEditForm.closedDate"
                    [required]="ticketEditForm.ticketStatus === 3"
                    readonly
                    autocomplete="off"
                    [disabled]="ticketEditForm.ticketStatus !== 3"
                  />
                  <nb-datepicker #closedDatePicker [min]="minDate"></nb-datepicker>
                  <div class="error-message-section">
                    <sfl-error-msg
                      [control]="closedDate"
                      [isFormSubmitted]="bulkEditForm?.submitted"
                      fieldName="Closed date"
                    ></sfl-error-msg>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <!-- Billing Status -->
          <div class="row my-2 align-items-center justify-content-start">
            <div class="col-md-2">
              <nb-checkbox
                status="basic"
                name="billingStatusSelection"
                #billingStatusSelection="ngModel"
                id="billingStatusSelection"
                class="sfl-track-checkbox"
                [checked]="availableFields.billingStatus"
                [(ngModel)]="availableFields.billingStatus"
                (change)="addRemoveBulkEditFields($event.target.checked, 2)"
              >
                Billing Status
              </nb-checkbox>
            </div>
            <div class="col-md-3">
              <div>
                <ng-select
                  name="selectedBillingStatus"
                  id="selectedBillingStatus"
                  [items]="ticketBillingStatuses"
                  bindLabel="description"
                  bindValue="ticketBillingStatusID"
                  notFoundText="No Billing Status Found"
                  placeholder="Select Billing Status"
                  #selectedBillingStatus="ngModel"
                  [(ngModel)]="ticketEditForm.billingStatus"
                  [clearable]="false"
                  [required]="availableFields.billingStatus"
                  [disabled]="!availableFields.billingStatus"
                >
                </ng-select>
                <!-- (change)="updateTicketBillingStatus(ticketEditForm.billingStatus)" -->
                <div class="error-message-section">
                  <sfl-error-msg
                    [control]="selectedBillingStatus"
                    [isFormSubmitted]="bulkEditForm?.submitted"
                    fieldName="Billing Status"
                  ></sfl-error-msg>
                </div>
              </div>
            </div>
          </div>
          <!-- Priority -->
          <div class="row my-2 align-items-center justify-content-start">
            <div class="col-md-2">
              <nb-checkbox
                status="basic"
                name="prioritySelection"
                id="prioritySelection"
                #prioritySelection="ngModel"
                class="sfl-track-checkbox"
                [checked]="availableFields.priority"
                [(ngModel)]="availableFields.priority"
                (change)="addRemoveBulkEditFields($event.target.checked, 3)"
              >
                Priority
              </nb-checkbox>
            </div>
            <div class="col-md-3">
              <div>
                <ng-select
                  name="priorityId"
                  [items]="priorityList"
                  bindLabel="name"
                  bindValue="id"
                  #priorityId="ngModel"
                  [(ngModel)]="ticketEditForm.priority"
                  notFoundText="No Priority Found"
                  placeholder="Select Priority"
                  [clearable]="false"
                  [required]="availableFields.priority"
                  [disabled]="!availableFields.priority"
                >
                </ng-select>
                <div class="error-message-section">
                  <sfl-error-msg [control]="priorityId" [isFormSubmitted]="bulkEditForm?.submitted" fieldName="Priority"></sfl-error-msg>
                </div>
              </div>
            </div>
          </div>
          <!-- Issue -->
          <div class="row my-2 align-items-center justify-content-start">
            <div class="col-md-2">
              <nb-checkbox
                status="basic"
                name="issueSelection"
                id="issueSelection"
                #issueSelection="ngModel"
                class="sfl-track-checkbox"
                [checked]="availableFields.issue"
                [(ngModel)]="availableFields.issue"
                (change)="addRemoveBulkEditFields($event.target.checked, 4)"
              >
                Issue
              </nb-checkbox>
            </div>
            <div class="col-md-10 d-flex align-items-center justify-content-start">
              <div>
                <nb-radio-group
                  class="d-flex"
                  name="issueAction"
                  #issueAction="ngModel"
                  [required]="availableFields.issue"
                  [disabled]="!availableFields.issue"
                  [(ngModel)]="ticketEditForm.issueAction"
                >
                  <nb-radio [value]="1">Append</nb-radio>
                  <nb-radio [value]="2">Overwrite</nb-radio>
                </nb-radio-group>
                <div class="error-message-section">
                  <sfl-error-msg
                    [control]="issueAction"
                    [isFormSubmitted]="bulkEditForm?.submitted"
                    fieldName="Issue Action"
                  ></sfl-error-msg>
                </div>
              </div>
              <div class="col-md-6">
                <label class="label" for="issueText">
                  Issue Text <span *ngIf="availableFields.issue" class="ms-1 text-danger">*</span>
                </label>
                <div>
                  <textarea
                    nbInput
                    placeholder="Issue Text"
                    rows="1"
                    [(ngModel)]="ticketEditForm.issueTxt"
                    name="issueText"
                    #issueText="ngModel"
                    id="issue"
                    placeholder="Issue"
                    maxlength="5120"
                    [required]="availableFields.issue"
                    [disabled]="!availableFields.issue"
                    fullWidth
                  >
                  </textarea>
                  <div class="error-message-section">
                    <sfl-error-msg [control]="issueText" [isFormSubmitted]="bulkEditForm?.submitted" fieldName="Issue"></sfl-error-msg>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <!-- Production Loss -->
          <div class="row my-2 align-items-center justify-content-start">
            <div class="col-md-2">
              <nb-checkbox
                status="basic"
                name="productionLossSelection"
                id="productionLossSelection"
                #productionLossSelection="ngModel"
                class="sfl-track-checkbox"
                [checked]="availableFields.productionLoss"
                [(ngModel)]="availableFields.productionLoss"
                (change)="addRemoveBulkEditFields($event.target.checked, 5)"
              >
                Production Loss
              </nb-checkbox>
            </div>
            <div class="col-md-10 d-flex align-items-center justify-content-start">
              <div>
                <nb-radio-group
                  class="d-flex"
                  name="productionLoss"
                  #productionLoss="ngModel"
                  [required]="availableFields.productionLoss"
                  [disabled]="!availableFields.productionLoss"
                  [(ngModel)]="ticketEditForm.productionLoss"
                >
                  <nb-radio [value]="2">---</nb-radio>
                  <nb-radio [value]="1">No</nb-radio>
                  <nb-radio [value]="0">Yes</nb-radio>
                </nb-radio-group>
                <div class="error-message-section">
                  <sfl-error-msg
                    [control]="productionLoss"
                    [isFormSubmitted]="bulkEditForm?.submitted"
                    fieldName="Production Loss"
                  ></sfl-error-msg>
                </div>
              </div>
              <div class="col-md-7">
                <div class="row w-100">
                  <div class="col-md-4">
                    <label class="label" for="input-Portfolio">Affected kWac</label>
                    <div class="d-flex align-items-center">
                      <input
                        nbInput
                        name="affectedKWac"
                        #affectedKWac="ngModel"
                        [(ngModel)]="ticketEditForm.affectedKWAC"
                        [disabled]="!availableFields.productionLoss"
                        placeholder="Affected kWac"
                        fullWidth
                        sflValidators
                      />
                    </div>
                  </div>
                  <div class="col-md-4" *ngIf="ticketEditForm.productionLoss === 0">
                    <label class="label" for="input-Portfolio">Loss Type</label>
                    <div>
                      <ng-select
                        name="prodLossType"
                        [items]="lossTypeList"
                        bindLabel="name"
                        bindValue="id"
                        #prodLossType="ngModel"
                        [(ngModel)]="ticketEditForm.lossType"
                        [disabled]="!availableFields.productionLoss"
                        notFoundText="No Production Loss Found"
                        placeholder="Select Production Loss"
                        [clearable]="false"
                      >
                      </ng-select>
                    </div>
                  </div>
                  <div class="col-md-4" *ngIf="ticketEditForm.productionLoss === 0">
                    <label class="label" for="input-Portfolio">Est Kwh Loss</label>
                    <div class="d-flex align-items-center">
                      <input
                        nbInput
                        name="estKWHLoss"
                        #estKWHLoss="ngModel"
                        [(ngModel)]="ticketEditForm.estKWHLoss"
                        [disabled]="!availableFields.productionLoss"
                        placeholder="Est Kwh Loss"
                        fullWidth
                        sflValidators
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <!-- Add Comment -->
          <div class="row my-2 align-items-center justify-content-start">
            <div class="col-md-2">
              <nb-checkbox
                status="basic"
                name="commentSelection"
                id="commentSelection"
                #commentSelection="ngModel"
                class="sfl-track-checkbox"
                [(ngModel)]="availableFields.comment"
                [checked]="availableFields.comment"
                (change)="addRemoveBulkEditFields($event.target.checked, 6)"
              >
                Add Comment</nb-checkbox
              >
            </div>
            <div class="col-md-10">
              <div class="col-md-6">
                <label class="label" for="issue"> Comment <span *ngIf="availableFields.comment" class="ms-1 text-danger">*</span> </label>
                <div>
                  <textarea
                    nbInput
                    rows="1"
                    placeholder="Comment"
                    name="Comment"
                    #Comment="ngModel"
                    [(ngModel)]="ticketEditForm.comment"
                    id="Comments"
                    maxlength="5120"
                    [required]="availableFields.comment"
                    [disabled]="!availableFields.comment"
                    fullWidth
                  >
                  </textarea>
                  <div class="error-message-section">
                    <sfl-error-msg [control]="Comment" [isFormSubmitted]="bulkEditForm?.submitted" fieldName="Comment"></sfl-error-msg>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <!-- Add Activity -->
          <div class="row my-2 justify-content-start">
            <div class="col-md-2 pt-md-4">
              <nb-checkbox
                status="basic"
                name="activitySelection"
                id="activitySelection"
                #activitySelection="ngModel"
                class="sfl-track-checkbox"
                [checked]="availableFields.activity"
                [(ngModel)]="availableFields.activity"
                (change)="addRemoveBulkEditFields($event.target.checked, 7)"
              >
                Add Activity
              </nb-checkbox>
            </div>
            <div class="col-md-10">
              <div class="row w-100">
                <div class="col-md-3">
                  <div>
                    <label class="label" for="activityDate"
                      >Activity Date<span *ngIf="availableFields.activity" class="ms-1 text-danger">*</span></label
                    >
                    <input
                      nbInput
                      name="activityDate"
                      placeholder="Select Activity Date"
                      fullWidth
                      [nbDatepicker]="activityDatePicker"
                      #activityDate="ngModel"
                      [(ngModel)]="ticketEditForm.activityDate"
                      [required]="availableFields.activity"
                      [disabled]="!availableFields.activity"
                      readonly
                      autocomplete="off"
                    />
                    <nb-datepicker #activityDatePicker [min]="minDate"></nb-datepicker>
                    <div class="error-message-section">
                      <sfl-error-msg
                        [control]="activityDate"
                        [isFormSubmitted]="bulkEditForm?.submitted"
                        fieldName="Activity Date"
                      ></sfl-error-msg>
                    </div>
                  </div>
                </div>
                <div class="col-md-6">
                  <div>
                    <label class="label"
                      >Activity Description<span *ngIf="availableFields.activity" class="ms-1 text-danger">*</span></label
                    >
                    <textarea
                      nbInput
                      fullWidth
                      rows="1"
                      placeholder="Activity Description"
                      [(ngModel)]="ticketEditForm.description"
                      name="description"
                      #description="ngModel"
                      id="input-woDescription"
                      maxlength="5120"
                      [required]="availableFields.activity"
                      [disabled]="!availableFields.activity"
                    >
                    </textarea>
                    <div class="error-message-section">
                      <sfl-error-msg
                        [control]="description"
                        [isFormSubmitted]="bulkEditForm?.submitted"
                        fieldName="Activity Description"
                      ></sfl-error-msg>
                    </div>
                  </div>
                </div>
              </div>
              <div class="row w-100">
                <div class="col-md-3">
                  <div>
                    <label class="label" for="input-resolved">Resolved</label>
                    <nb-radio-group
                      class="d-flex"
                      name="resolved"
                      #resolved="ngModel"
                      [(ngModel)]="ticketEditForm.isResolved"
                      [disabled]="!availableFields.activity || disabledResolved"
                    >
                      <nb-radio [value]="true">Yes</nb-radio>
                      <nb-radio [value]="false">No</nb-radio>
                    </nb-radio-group>
                    <div class="error-message-section">
                      <sfl-error-msg [control]="resolved" [isFormSubmitted]="bulkEditForm?.submitted" fieldName="Resolved"></sfl-error-msg>
                    </div>
                  </div>
                </div>
              </div>
              <!-- <div class="row w-100">
                <div class="col-md-3">
                  <div>
                    <label class="label" for="input-Portfolio">Materials</label>
                    <div class="d-flex align-items-center">
                      <input
                        nbInput
                        name="materials"
                        placeholder="Materials"
                        [(ngModel)]="ticketEditForm.materials"
                        fullWidth
                        [disabled]="!availableFields.activity"
                      />
                    </div>
                  </div>
                </div>
                <div class="col-md-3">
                  <div>
                    <label class="label" for="input-Portfolio">Materials Cost</label>
                    <div class="d-flex align-items-center">
                      <input
                        nbInput
                        name="materialsCost"
                        [(ngModel)]="ticketEditForm.materialCost"
                        [disabled]="!availableFields.activity"
                        placeholder="Materials Cost"
                        fullWidth
                      />
                    </div>
                  </div>
                </div>
              </div> -->
            </div>
          </div>
        </div>
        <div *ngIf="currentStep === 3">
          <div>
            <nb-radio-group class="d-flex" name="ticketStatus" required [(ngModel)]="ticketEditForm.isSendEmail">
              <nb-radio [value]="true">Send Email(s)</nb-radio>
              <nb-radio [value]="false">Do Not Send Email(s)</nb-radio>
            </nb-radio-group>
          </div>
        </div>
        <div *ngIf="currentStep === 4">
          <div>
            <p>{{ selectedBulkEditTickets?.length }} Tickets will be updated</p>
            <div>
              <table class="table table-striped">
                <thead>
                  <tr>
                    <th>Field Name</th>
                    <th>Action</th>
                    <th>Value</th>
                  </tr>
                </thead>
                <tbody>
                  <tr *ngFor="let field of getTableData()">
                    <td>{{ field.fieldName }}</td>
                    <td>
                      {{ field.action }}
                    </td>
                    <td>{{ field.value }}</td>
                  </tr>
                </tbody>
              </table>
            </div>
            Email Notification {{ ticketEditForm.isSendEmail ? 'WILL' : 'WILL NOT' }} be sent for this update
          </div>
        </div>
        <div *ngIf="currentStep === 5">
          <div>
            <p>Status</p>
            <p>
              Ticket bulk edit operation {{ apiCount === selectedBulkEditTickets?.length ? 'completed successfully' : 'in progress...' }}
            </p>
            <p>Editing {{ selectedBulkEditTickets?.length }} Tickets</p>
            <p>{{ completedCount }}/{{ selectedBulkEditTickets?.length }} Complete</p>
            <ng-container *ngIf="failedEntityNumber?.length">
              <p class="fw-bold">Some records are not processed due to InActive Site(s), below is detail.</p>
              <ul class="multi-column-list">
                <li *ngFor="let entity of failedEntityNumber">
                  {{ entity }}
                </li>
              </ul>
            </ng-container>
          </div>
          <div class="mt-3">
            <button
              class="linear-mode-button ms-2 button-h-100"
              nbButton
              size="small"
              type="button"
              (click)="onCancel()"
              [disabled]="apiCount !== selectedBulkEditTickets?.length"
            >
              Done
            </button>
          </div>
        </div>
      </form>
    </div>
  </nb-card-body>
</nb-card>
<context-menu #actionMenu>
  <ng-template contextMenuItem (execute)="openLink($event.value.data, false)"> Open link in new tab </ng-template>
  <ng-template contextMenuItem (execute)="openLink($event.value.data, true)"> Open link in new window </ng-template>
</context-menu>

<ng-template #truckRollModal>
  <div class="modal-header">
    <h4 class="modal-title pull-left">Truck Roll Numbers</h4>
    <button type="button" class="btn-close close pull-right" aria-label="Close" (click)="modalRef?.hide()">
      <span class="d-inline-block scale-2">&times;</span>
    </button>
  </div>
  <div class="modal-body">
    <div class="container">
      <ul class="truckroll-ul">
        <li class="truckroll-li" *ngFor="let truckNumber of selectedTicketForTruckRoll?.truckRollNumbers">
          <span (click)="redirectToTruckRollScreen(truckNumber)"> {{ truckNumber }}</span>
        </li>
      </ul>
    </div>
  </div>
</ng-template>

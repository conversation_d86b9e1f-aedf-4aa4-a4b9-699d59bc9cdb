import { Location } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { FormArray, FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { BsModalRef, BsModalService, ModalOptions } from 'ngx-bootstrap/modal';
import { Subscription, filter } from 'rxjs';
import { SignatureComponent } from '../../../../@shared/components/signature/signature.component';
import { AppConstants } from '../../../../@shared/constants';
import { Dropdown } from '../../../../@shared/models/dropdown.model';
import {
  HazardDetailByWorkType,
  hazardsRiskLevelList,
  WorkStepAndHazardListByWorkType,
  WorkStepDetailByWorkType,
  WorkTypes
} from '../../../../@shared/models/jha.model';
import { MessageVM } from '../../../../@shared/models/messageVM.model';
import { AllReportModel, MasterReportModel, ReportTypes, SiteAuditNewJhaModel } from '../../../../@shared/models/report.model';
import { AlertService } from '../../../../@shared/services';
import { StorageService } from '../../../../@shared/services/storage.service';
import { CustomerService } from '../../../customer-management/customer.service';
import { PortfolioService } from '../../../portfolio-management/portfolio.service';
import { ReportService } from '../../../report/report.service';
import { SiteService } from '../../../site-management/site.service';
import { GeneralInfo, LOTOBarrier } from '../../settings/settings.model';
import { SettingService } from '../../settings/settings.service';
import { WorkStep } from '../../settings/work-type/work-type.model';
import { WorkTypeService } from '../../settings/work-type/work-type.service';
import { SiteCheckInService } from '../../site-checkin/site-checkin.service';
import { CrewSelectionEvent, JHAAddEditOpenFromSource } from './jha-add-edit.model';
import { JhaUploadService } from './jha-upload.service';
import { JhaHelperService } from '../jha-helper.service';

@Component({
  selector: 'sfl-jha-add-edit',
  templateUrl: './jha-add-edit.component.html',
  styleUrls: ['./jha-add-edit.component.scss']
})
export class JhaAddEditComponent implements OnInit {
  loading = false;
  isEdit = false;
  showCrew = false;
  loadWorkStep = false;
  loadCrewLeads = false;
  isSign = false;
  hideDropdown = false;
  generatedIds: string[] = [];
  summaryForm: FormGroup;
  crewDataForm: FormGroup;
  customerData: Dropdown[] = [];
  portData: Dropdown[] = [];
  siteList: Dropdown[] = [];
  workTypeList: Dropdown[] = [];
  workStepList: WorkStep[] = [];
  workStepDetail = [];
  siteContact: any;
  modalRef: BsModalRef;
  generalInfo: GeneralInfo = new GeneralInfo();
  subscription: Subscription = new Subscription();
  contactNoFormat = AppConstants.phoneNumberMask;
  isDynamicJha: boolean = false;
  riskLevelList = [
    { name: 'High', id: 1 },
    { name: 'Medium', id: 2 },
    { name: 'Low', id: 3 }
  ];
  user: any;
  jhaId: string;
  userList: Dropdown[] = [];
  showPreview = false;
  activeTab: string = 'Summary';
  jhaDetails;
  details = { checkin: 0, cust: 0, port: 0, site: 0, workorderId: 0, workOrderNumber: '', reportId: 0 };
  reportMasterList: AllReportModel = new AllReportModel();
  reportList: MasterReportModel[] = [];
  fromWhere: any;
  customerListLoading = false;
  portfolioListLoading = false;
  siteListLoading = false;

  constructor(
    private fb: FormBuilder,
    private readonly alertService: AlertService,
    private readonly _location: Location,
    private readonly portfolioService: PortfolioService,
    private readonly customerService: CustomerService,
    private readonly siteService: SiteService,
    private readonly jhaUploadService: JhaUploadService,
    private readonly settingService: SettingService,
    private readonly modalService: BsModalService,
    private readonly workTypeService: WorkTypeService,
    private readonly storageService: StorageService,
    private readonly route: ActivatedRoute,
    private readonly reportService: ReportService,
    private readonly router: Router,
    private readonly siteCheckInService: SiteCheckInService,
    private readonly jhaHelperService: JhaHelperService
  ) {}

  ngOnInit(): void {
    this.user = this.storageService.get('user');
    this.summaryForm = this.fb.group({
      customerId: new FormControl('', !this.isDynamicJha ? Validators.required : null),
      portfolioId: new FormControl('', !this.isDynamicJha ? Validators.required : null),
      siteId: new FormControl('', !this.isDynamicJha ? Validators.required : null),
      siteContactName: new FormControl(''),
      siteContactPhone: new FormControl(''),
      jhaGuid: new FormControl(''),
      reportId: new FormControl(''),
      jhaDate: new FormControl(new Date(), Validators.required),
      mobWorkSteps: new FormArray([]),
      workTypes: new FormArray([], Validators.required),
      workTypeIds: new FormControl([], Validators.required),
      crewData: new FormArray([]),
      customerName: new FormControl('', this.isDynamicJha ? Validators.required : null),
      portfolioName: new FormControl('', this.isDynamicJha ? Validators.required : null),
      siteName: new FormControl('', this.isDynamicJha ? Validators.required : null),
      isSiteAuditJHA: new FormControl(false, this.isDynamicJha ? Validators.required : null),
      listOfWorkStep: new FormArray([]),
      listOfHazard: new FormArray([]),
      lotoBarrier: new FormControl(null)
    });
    this.route.queryParams.subscribe(async params => {
      if (params && Number(params.checkin)) {
        this.details.checkin = Number(params.checkin);
        this.details.cust = Number(params.cust);
        this.details.port = Number(params.port);
        this.details.site = Number(params.site);

        this.summaryForm.controls['customerId'].setValue(Number(params.cust));
        this.summaryForm.controls['portfolioId'].setValue(Number(params.port));

        await this.onCustomerSelect();
        await this.onPortfolioSelect();
        this.summaryForm.controls['siteId'].setValue(Number(params.site));
        this.onSiteSelect({ id: Number(params.site) });
        this.summaryForm.get('customerId').disable();
        this.summaryForm.get('portfolioId').disable();
        this.summaryForm.get('siteId').disable();
        this.summaryForm.get('jhaDate').disable();
      }
    });

    this.route.params.subscribe(params => {
      if (params && params.id && params.mode) {
        this.jhaId = params.id;
        if (params.mode === 'edit') {
          this.isEdit = true;
        }
        if (params.mode !== 'add' && params.id) {
          this.getJHADetailById(params.id);
        }
        this.setSiteAuditNewJHAAddModel(params);
      } else {
        this.addCrew(true);
        this.getCustomerAccess();
      }
    });
    this.getWorkTypeList();
    this.getGeneralInfo();
    this.getLOTOBarrier();
    this.getAllCrewLead();

    for (let i = 0; i < 20; i++) {
      let id = this.newGuid();
      this.generatedIds.push(id);
    }
    this.jhaGuid.patchValue(this.generatedIds[0]);
    this.reportId.patchValue(this.generatedIds[1]);

    this.route.queryParams.subscribe(async params => {
      if (params && Number(params.checkin)) {
        this.details.checkin = Number(params.checkin);
        this.details.cust = Number(params.cust);
        this.details.port = Number(params.port);
        this.details.site = Number(params.site);
        this.details.workorderId = Number(params.workorderId);
        this.details.reportId = params.reportId;
        this.details.workOrderNumber = params.workOrderNumber;
        this.fromWhere = params.returnTo;

        this.summaryForm.controls['customerId'].setValue(Number(params.cust));
        this.summaryForm.controls['portfolioId'].setValue(Number(params.port));

        await this.onCustomerSelect();
        await this.onPortfolioSelect();
        this.summaryForm.controls['siteId'].setValue(Number(params.site));
        this.onSiteSelect({ id: Number(params.site) });
        this.summaryForm.get('customerId').disable();
        this.summaryForm.get('portfolioId').disable();
        this.summaryForm.get('siteId').disable();
        this.summaryForm.get('jhaDate').disable();
      }
    });
  }

  setSiteAuditNewJHAAddModel(params): void {
    const activatedRouteSnapshotData = this.route.snapshot.data;
    if (
      params.mode === 'add' &&
      activatedRouteSnapshotData &&
      activatedRouteSnapshotData['jhaAddEditSource'] === JHAAddEditOpenFromSource.SITE_AUDIT_JHA
    ) {
      const localSiteAuditNewJhaModel = this.storageService.get('siteAuditNewJhaModel');
      const siteAuditNewJhaModel = new SiteAuditNewJhaModel(localSiteAuditNewJhaModel);
      if (this.jhaId === siteAuditNewJhaModel.jhaGuid) {
        siteAuditNewJhaModel.isSiteAuditJHA = activatedRouteSnapshotData['jhaAddEditSource'] === JHAAddEditOpenFromSource.SITE_AUDIT_JHA;
        if (siteAuditNewJhaModel.isSiteAuditJHA === true) {
          this.isDynamicJha = true;
        }
        siteAuditNewJhaModel.siteContactPhone = siteAuditNewJhaModel.siteContactPhone === 0 ? '' : siteAuditNewJhaModel.siteContactPhone;
        this.summaryForm.get('customerId').disable();
        this.summaryForm.get('portfolioId').disable();
        this.summaryForm.get('siteId').disable();
        this.summaryForm.patchValue(siteAuditNewJhaModel);
        this.addCrew(true);
        this.getCustomerAccess();
      } else {
        this.storageService.clear('siteAuditNewJhaModel');
      }
    }
  }

  getJHADetailById(id) {
    this.loading = true;
    this.subscription.add(
      this.jhaUploadService.getJHADetailById(id).subscribe({
        next: (res: any) => {
          if (res.isSiteAuditJHA === true) {
            this.isDynamicJha = true;
          }
          res.jhaDate =
            res.jhaDate && res.jhaDate !== '0001-01-01T00:00:00' && res.jhaDate !== '' && res.jhaDate !== null
              ? new Date(res.jhaDate)
              : new Date();
          res.siteContactPhone = res.siteContactPhone ? res.siteContactPhone : '';
          this.jhaDetails = res;
          this.summaryForm.patchValue(res);
          if (res.workTypes && res.workTypes.length) {
            this.setWorkTypesArrayFormArray(res.workTypes, false);
            const workTypeIds = res.workTypes.map(item => ({ workTypeId: item.workTypeId, workTypeName: item.workTypeName }));
            this.summaryForm.get('workTypeIds').patchValue(workTypeIds);
          }
          if (res?.listOfWorkStep && res?.listOfWorkStep?.length) {
            this.setListOfWorkStepArrayFormArray(res.listOfWorkStep, true);
          }
          if (res?.listOfHazard && res?.listOfHazard?.length) {
            this.setListOfHazardArrayFormArray(res.listOfHazard, true);
          }
          for (const [i, v] of res.crewData.entries()) {
            const form = this.fb.group({
              crew: new FormControl('', Validators.required),
              crewPhone: new FormControl(''),
              isCrewLead: v.isCrewLead ? new FormControl(true) : new FormControl(false),
              signature: new FormControl('', Validators.required)
            });
            this.crewData.push(form);
            v.crewPhone = v.crewPhone === 0 ? null : v.crewPhone;
            this.crewData.at(i).patchValue(v);
          }
          this.getCustomerAccess();
          this.onPortfolioSelect();
          this.loading = false;
        },
        error: e => {
          this.loading = false;
        }
      })
    );
  }

  newGuid() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
      let r = (Math.random() * 16) | 0,
        v = c == 'x' ? r : (r & 0x3) | 0x8;
      return v.toString(16);
    });
  }

  get workTypesArray(): FormArray {
    return this.summaryForm.controls['workTypes'] as FormArray;
  }

  get listOfWorkStepArray(): FormArray {
    return this.summaryForm.controls['listOfWorkStep'] as FormArray;
  }

  get listOfHazardArray(): FormArray {
    return this.summaryForm.controls['listOfHazard'] as FormArray;
  }

  get jhaGuid(): FormControl {
    return this.summaryForm.get('jhaGuid') as FormControl;
  }

  get reportId(): FormControl {
    return this.summaryForm.get('reportId') as FormControl;
  }

  get lotoBarrier(): FormControl {
    return this.summaryForm.get('lotoBarrier') as FormControl;
  }

  get crewData(): FormArray {
    return this.summaryForm.controls['crewData'] as FormArray;
  }

  get isValidCrewDataForSummaryTab(): boolean {
    return this.crewData.controls.every(control => {
      if (control instanceof FormGroup) {
        const crew = control.get('crew')?.valid ?? false;
        const crewPhone = control.get('crewPhone')?.valid ?? false;
        return crew && crewPhone;
      }
      return false;
    });
  }

  get isAnyWorkStepIncluded(): boolean {
    return this.listOfWorkStepArray.value.some(control => control.isIncludedWorkStep);
  }

  get isAnyHazardsIncluded(): boolean {
    return this.listOfHazardArray.value.some(control => control.isIncludedHazard);
  }

  get summary(): FormGroup {
    return this.summaryForm as FormGroup;
  }

  getAllCrewLead(): void {
    this.loadCrewLeads = true;
    this.subscription.add(
      this.jhaUploadService.getAllCrewLead().subscribe({
        next: (res: Dropdown[]) => {
          this.userList = res;
          this.loadCrewLeads = false;
        },
        error: e => {
          this.loadCrewLeads = false;
        }
      })
    );
  }

  changeCrewLead(event: CrewSelectionEvent, index: number) {
    if (this.isDuplicateEntry(event)) {
      this.crewData.at(index).patchValue({
        crew: null,
        crewPhone: '',
        isCrewLead: false,
        signature: ''
      });
      this.alertService.showErrorToast('This user is already in use.');
      return;
    }
    if (event) {
      for (let i = 0; i < this.crewData.length; i++) {
        if (this.crewData.value[0].crew === event.name) {
          this.crewData.at(0).get('crewPhone').patchValue(event?.phoneNumber);
        }
        // need to remove the signature if crew lead gets updated/ changed
        if (this.crewData.value[i].crew === event.name) {
          this.crewData.at(i).get('signature').setValue('');
        }
      }
      // Preserve the signature if the crew member is changed back to the stored one in res
      const crewIndex = this.crewData.value.findIndex(x => x.crew === event.name);
      this.jhaDetails?.crewData.forEach((element, index) => {
        if (element.crew === event.name && crewIndex !== -1) {
          this.crewData.at(crewIndex).get('signature').patchValue(this.jhaDetails.crewData[index].signature);
        }
      });
    }
  }

  private isDuplicateEntry(event: CrewSelectionEvent): boolean {
    if (event) {
      return this.crewData.value.filter(user => user.crew?.toLowerCase() === event.name?.toLowerCase()).length > 1;
    }
    return false;
  }

  addCrew(isPhoneRequired = false) {
    const name = isPhoneRequired ? `${this.user.firstName} ${this.user.lastName}` : null;
    const phone = isPhoneRequired ? this.user.phone : '';
    const form = this.fb.group({
      crew: new FormControl(name, Validators.required),
      crewPhone: new FormControl(phone),
      isCrewLead: isPhoneRequired ? new FormControl(true) : new FormControl(false),
      signature: new FormControl('', Validators.required)
    });
    this.crewData.push(form);
  }

  initWorkName() {
    return this.fb.group({
      id: new FormControl(''),
      workTypeName: new FormControl('', Validators.required),
      workTypeId: new FormControl(),
      workSteps: this.fb.array([])
    });
  }

  initWorkStep() {
    return this.fb.group({
      workStepName: new FormControl(''),
      hazards: new FormControl(''),
      riskLevel: new FormControl(''),
      controlBarriers: new FormControl(''),
      protectiveBarriers: new FormControl(''),
      supportBarriers: new FormControl(''),
      rowCount: new FormControl(''),
      reportWorkTypeId: new FormControl(''),
      isCopy: new FormControl(false),
      copyGroup: new FormControl('')
    });
  }

  sendEmail() {
    this.loading = true;
    this.jhaUploadService.sendEmail(this.jhaId).subscribe({
      next: (res: any) => {
        this.alertService.showSuccessToast(res.message);
        this.loading = false;
      },
      error: e => {
        this.loading = false;
      }
    });
  }

  setWorkTypesArrayFormArray(workTypes: WorkTypes[], isNeedToCallApi: boolean = true): void {
    const formGroups = workTypes
      .map(item => new WorkTypes(item.workTypeId, item.workTypeName, item.reportId ?? this.reportId.value ?? '', item.id))
      .map(x => this.jhaHelperService.initOrPatchWorkType(x, this.reportId.value));
    this.workTypesArray.clear();
    formGroups.forEach(group => this.workTypesArray.push(group));
    if (workTypes) {
      this.initGetWorkStepAndHazardsByWorkTypeId(workTypes, isNeedToCallApi);
    } else {
      this.workTypesArray.clear();
      this.summaryForm.get('workTypeIds').setValue([]);
      this.listOfWorkStepArray.clear();
      this.listOfHazardArray.clear();
    }
  }

  initGetWorkStepAndHazardsByWorkTypeId(workTypes: WorkTypes[], isNeedToCallApi: boolean = true): void {
    const workTypeIds = workTypes.map(x => x.workTypeId);

    if (isNeedToCallApi) {
      this.getWorkStepAndHazardsByWorkTypeId(workTypeIds);
    }
  }

  onChangeWorkType(event: WorkTypes[]): void {
    this.loadWorkStep = true;
    this.setWorkTypesArrayFormArray(event);
  }

  getWorkStepAndHazardsByWorkTypeId(workTypeIds: number[]): void {
    this.loadWorkStep = true;
    this.subscription.add(
      this.jhaUploadService.getWorkStepAndHazardsByWorkTypeId(workTypeIds).subscribe({
        next: (res: WorkStepAndHazardListByWorkType) => {
          res.listOfHazard = res.listOfHazard.map(
            item =>
              ({
                ...item,
                riskLevelId: hazardsRiskLevelList.find(x => x.nameStr === item.riskLevel)?.id
              } as HazardDetailByWorkType)
          );
          this.setListOfWorkStepArrayFormArray(res.listOfWorkStep);
          this.setListOfHazardArrayFormArray(res.listOfHazard);
          this.loadWorkStep = false;
        },
        error: e => {
          this.loadWorkStep = false;
        }
      })
    );
  }

  setListOfWorkStepArrayFormArray(listOfWorkStep: WorkStepDetailByWorkType[], isGetByIdCall: boolean = false): void {
    const selectedWorkStepsIds = this.listOfWorkStepArray.value.filter(item => item.isIncludedWorkStep).map(item => item.workStepId);
    const formGroups = listOfWorkStep
      .map(
        item =>
          new WorkStepDetailByWorkType(
            item.workStepId,
            item.workStep,
            item.equipmentType,
            item.equipmentModel,
            item.asBuildPageNumber,
            item.isLotoWorkStep,
            item.orderValue,
            isGetByIdCall ? item.isIncludedWorkStep : selectedWorkStepsIds.includes(item.workStepId),
            item.listOfWorkType
          )
      )
      .map(x => this.jhaHelperService.initOrPatchListOfWorkSteps(x));
    this.listOfWorkStepArray.clear();
    formGroups.forEach(group => this.listOfWorkStepArray.push(group));
  }

  setListOfHazardArrayFormArray(listOfHazard: HazardDetailByWorkType[], isGetByIdCall: boolean = false): void {
    const selectedHazardsIds = this.listOfHazardArray.value.filter(item => item.isIncludedHazard).map(item => item.hazardId);
    const formGroups = listOfHazard
      .map(
        item =>
          new HazardDetailByWorkType(
            item.hazardId,
            item.hazards,
            item.riskLevel,
            item.riskLevelId,
            item.controlBarriers,
            item.protectiveBarriers,
            item.supportBarriers,
            item.isLotoHazard,
            item.hazardOrder,
            isGetByIdCall ? item.isIncludedHazard : selectedHazardsIds.includes(item.hazardId),
            item.listOfWorkStep
          )
      )
      .map(x => this.jhaHelperService.initOrPatchListOfHazards(x));
    this.listOfHazardArray.clear();
    formGroups.forEach(group => this.listOfHazardArray.push(group));
  }

  getGeneralInfo() {
    this.loading = true;
    this.settingService.getGeneralInfo().subscribe({
      next: (res: GeneralInfo) => {
        this.generalInfo = res;
        this.loading = false;
      },
      error: e => {
        this.loading = false;
      }
    });
  }

  getSiteContactDetail(id) {
    this.loading = true;
    this.jhaUploadService.getSiteDetails(id).subscribe({
      next: (res: any) => {
        this.siteContact = res;
        this.summaryForm.controls['siteContactName'].patchValue(res.siteContact);
        this.summaryForm.controls['siteContactPhone'].patchValue(res.sitePhone ? res.sitePhone : '');
        this.loading = false;
      },
      error: e => {
        this.loading = false;
      }
    });
  }

  deleteCrew(id, i) {
    if (!id) {
      this.crewData.removeAt(i);
    } else if (id) {
      if (this.crewData.length > 1) {
        this.loading = true;
        this.subscription.add(
          this.jhaUploadService.deleteCrew(id).subscribe({
            next: (res: MessageVM) => {
              if (res) {
                this.crewData.removeAt(i);
                this.alertService.showSuccessToast(res.message);
                this.loading = false;
              }
            },
            error: _e => {
              this.loading = false;
            }
          })
        );
      } else {
        this.alertService.showWarningToast(' 1 Crew is required');
      }
    }
  }

  getComponentTitle() {
    if (this.isEdit) {
      return `Edit JHA`;
    } else {
      return `Create JHA`;
    }
  }

  getCustomerAccess() {
    this.customerListLoading = true;
    this.subscription.add(
      this.customerService.getAllCustomer().subscribe({
        next: res => {
          this.customerData = res;
          if (this.summaryForm.value.customerId) {
            this.onCustomerSelect();
          }
          this.customerListLoading = false;
        },
        error: e => {
          this.loading = false;
          this.customerListLoading = false;
        }
      })
    );
  }

  onSiteSelect(event) {
    this.showCrew = true;
    this.getSiteContactDetail(event.id);
  }

  onCustomerSelect(): Promise<void> {
    if (this.summaryForm.value.customerId) {
      return new Promise((resolve, reject) => {
        this.portfolioListLoading = true;
        this.subscription.add(
          this.portfolioService.getAllPortfoliosByCustomerId(this.isEdit, this.summaryForm.value.customerId).subscribe({
            next: res => {
              setTimeout(() => {
                if (!this.isEdit) {
                  const portList = res.filter(p => p.customerId === this.summaryForm.value.customerId && p.isActive);
                  this.portData = portList;
                } else {
                  this.portData = res;
                }
                this.portfolioListLoading = false;

                resolve();
              }, 0);
            },
            error: e => {
              this.loading = false;
              this.portfolioListLoading = false;
              reject();
            }
          })
        );
      });
    }
  }

  onPortfolioSelect(): Promise<void> {
    return new Promise(async resolve => {
      this.siteList = [];
      this.summaryForm.value.siteId = null;
      if (this.summaryForm.value.portfolioId) {
        await this.getAllSiteByPortfolio();
        resolve();
      }
    });
  }

  getAllSiteByPortfolio(): Promise<void> {
    return new Promise((resolve, reject) => {
      this.loading = true;
      this.siteListLoading = true;
      this.subscription.add(
        this.siteService.getAllSitesByPortfolioId(true, this.summaryForm.value.portfolioId).subscribe({
          next: (res: Dropdown[]) => {
            this.siteList = res;
            this.loading = false;
            this.siteListLoading = false;
            resolve();
          },
          error: e => {
            this.loading = false;
            this.siteListLoading = false;
            reject();
          }
        })
      );
    });
  }

  getWorkTypeList() {
    this.loading = true;
    this.subscription.add(
      this.jhaUploadService.getWorkType().subscribe({
        next: (res: any) => {
          this.workTypeList = res.result.map(item => ({ workTypeId: item.id, workTypeName: item.name }));
          this.loading = false;
        },
        error: e => {
          this.loading = false;
        }
      })
    );
  }

  getLOTOBarrier(): void {
    this.loading = true;
    this.subscription.add(
      this.settingService.getLOTOBarrier().subscribe({
        next: (res: LOTOBarrier) => {
          if (res && res.id && res.riskLevel) {
            res.name = res.name ? res.name : 'LOTO';
            this.lotoBarrier.patchValue(res);
          }
          this.loading = false;
        },
        error: (e: any) => {
          this.loading = false;
        }
      })
    );
  }

  getErrorNumber(form, tabId) {
    if (form.status === 'INVALID') {
      return document.querySelectorAll(`#${tabId} .input-error`).length;
    }
    return null;
  }

  getActiveTab(tab: string) {
    return this.activeTab === tab;
  }

  onStepSelect(event, i) {
    this.loading = true;
    if (event.workStepName == 'other') {
      this.hideDropdown = true;
    }
    this.subscription.add(
      this.jhaUploadService.getWorkStepList(event.id).subscribe({
        next: (res: any) => {
          res.forEach(element => {
            if (element.id == event.id) {
              // this.workTypes.at(i)?.get('workSteps').patchValue(event);
            }
          });
          this.loading = false;
        },
        error: e => {
          this.loading = false;
        }
      })
    );
  }

  addSign(i) {
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      initialState: {}
    };
    this.modalRef = this.modalService.show(SignatureComponent, ngModalOptions);
    this.modalRef.content.onClose.subscribe(result => {
      if (result) {
        this.isSign = true;
        this.crewData.at(i)?.get('signature').patchValue(result);
      }
    });
  }

  onSubmit() {
    if (this.summaryForm.valid) {
      if (this.details.cust) {
        this.summaryForm.get('customerId').enable();
        this.summaryForm.get('portfolioId').enable();
        this.summaryForm.get('siteId').enable();
        this.summaryForm.get('jhaDate').enable();
      }
      // this.loading = true;
      this.summaryForm.get('customerId').enable();
      this.summaryForm.get('portfolioId').enable();
      this.summaryForm.get('siteId').enable();
      this.summaryForm.get('jhaDate').enable();
      if (!this.isEdit) {
        this.summaryForm.value.id = 0;
        console.log(this.summaryForm.value);
        // this.subscription.add(
        //   this.jhaUploadService.uploadJha(this.summaryForm.value).subscribe({
        //     next: (res: MessageVM) => {
        //       if (this.details.checkin && this.details.workOrderNumber) {
        //         this.loading = true;
        //         // add the created jha to the report and navigate the user to the report if s/he came from the create new report flow
        //         // report is not created, user opted to create JHA. Create report and then Goto JHA
        //         // generate the report with empty JHA then redirect to JHA create screen and then report fill
        //         // const user = this.storageService.get('user');
        //         const date = new Date();
        //         const offset = -date.getTimezoneOffset(); // Get offset in minutes
        //         const offsetHours = String(Math.floor(Math.abs(offset) / 60)).padStart(2, '0');
        //         const offsetMinutes = String(Math.abs(offset) % 60).padStart(2, '0');
        //         const timeZoneOffset = (offset >= 0 ? '+' : '-') + offsetHours + ':' + offsetMinutes;
        //         const user = this.storageService.get('user');

        //         const localDateWithOffset =
        //           date.getFullYear() +
        //           '-' +
        //           String(date.getMonth() + 1).padStart(2, '0') +
        //           '-' +
        //           String(date.getDate()).padStart(2, '0') +
        //           'T' +
        //           String(date.getHours()).padStart(2, '0') +
        //           ':' +
        //           String(date.getMinutes()).padStart(2, '0') +
        //           ':' +
        //           String(date.getSeconds()).padStart(2, '0') +
        //           '.' +
        //           String(date.getMilliseconds()).padStart(3, '0') +
        //           timeZoneOffset;

        //         const generateNewReportData = {
        //           reportId: this.details.reportId, // we will get the report id from the query params
        //           reportName: this.details.workOrderNumber,
        //           siteId: this.details.site,
        //           reportTypeId: this.details.workOrderNumber.includes('SV-') ? ReportTypes.reportTypeSVId : ReportTypes.reportTypeMVPMId, // check here what type of wo it is then decide
        //           workorderId: this.details.workorderId,
        //           reporterId: user.userId,
        //           reportCreatedDate: localDateWithOffset,
        //           reportVersion: 3, // fixed to 3 for now
        //           miscellaneous: '',
        //           comment: null,
        //           uploadJHA: null,
        //           uploadEquipmentStatus: null,
        //           uploadChecklist: null,
        //           uploadNC: null,
        //           isFromMobileUploaded: false,
        //           uploadJHAV3ReportIds: [res.id],
        //           isFromWeb: true
        //         };

        //         this.reportService.createNewSVReportMobile(generateNewReportData).subscribe({
        //           next: res => {
        //             this.alertService.showSuccessToast(res.message);
        //             this.loading = false;
        //             // redirect the user to the generated report
        //             this.onViewReport(this.details.workOrderNumber.includes('SV-') ? 'SV' : 'MVPM');
        //           },
        //           error: e => {
        //             this.loading = false;
        //           }
        //         });
        //       } else {
        //         this.alertService.showSuccessToast(res.message);
        //         this.loading = false;
        //         this.goBack();
        //       }
        //     },
        //     error: e => {
        //       this.loading = false;
        //     }
        //   })
        // );
      } else {
        this.subscription.add(
          this.jhaUploadService.updateJha(this.summaryForm.value).subscribe({
            next: (res: MessageVM) => {
              this.alertService.showSuccessToast(res.message);
              this.loading = false;
              this.goBack();
            },
            error: e => {
              this.loading = false;
            }
          })
        );
      }
    } else {
      this.summaryForm.markAllAsTouched();
      this.alertService.showErrorToast('Please fill the mandatory fields to submit the form.');
    }
  }

  addNewUser = (name: string) => {
    return {
      name: name,
      id: null,
      tag: true
    };
  };

  addNewWorkType = (name: string) => {
    return {
      name: name,
      id: -1,
      tag: true
    };
  };

  changeTab(event) {
    this.activeTab = event.tabTitle;
    if (event.tabTitle === 'Preview') {
      this.showPreview = true;
    } else {
      this.showPreview = false;
    }
  }

  goBack() {
    this._location.back();
    this.storageService.clear('siteAuditNewJhaModel');
    setTimeout(() => {
      this.siteCheckInService.userObjectChangedInStorage$.next(false);
    }, 500);
  }

  onViewReport(reportType) {
    const id = this.details.workorderId;
    if (reportType === 'SV') {
      this.router.navigate(['/entities/reports/sitevisits/edit/' + id + '/false'], { queryParams: { returnTo: this.fromWhere } });
    } else {
      this.router.navigate(['/entities/reports/mvpm/edit/' + id + '/false'], { queryParams: { returnTo: this.fromWhere } });
    }
  }
}

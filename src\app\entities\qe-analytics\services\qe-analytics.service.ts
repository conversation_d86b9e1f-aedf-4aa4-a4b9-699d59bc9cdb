import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { QEAnalyticsPasswordObj, QEAnalyticsRes, QEMenuModuleType } from '../models/qe-analytics.model';
import { from, Observable } from 'rxjs';
import { ApiUrl, AppConstants } from '../../../@shared/constants';
import {
  FrontEndMenuOrderType,
  QE_MENU_MODULE_NAME_ENUM,
  QE_MENU_MODULE_NAME_ENUM_LIST,
  QE_MENU_MODULE_ORDER_LIST
} from '../../../@shared/enums/qe-menu.enum';
import { StorageService } from '../../../@shared/services/storage.service';
import { CommonFilter } from '../../../@shared/components/filter/common-filter.model';

@Injectable({
  providedIn: 'root'
})
export class QEAnalyticsService {
  qeMenuModuleList: QEMenuModuleType[] = [];
  constructor(private readonly http: HttpClient, private readonly storageService: StorageService) {}

  private menuOrderKeyNameGeneration = (parent: string, child: string): string => {
    return `${parent.replace(/ /g, '_').toLowerCase()}_${child.replace(/ /g, '_').toLowerCase()}`;
  };

  private getFlattenMenuOrderList(menuOrderObj: FrontEndMenuOrderType): Record<string, number> {
    const flattenMenuOrderObj: Record<string, number> = {};
    const rootMenuOrderKeys = new Set(Object.keys(menuOrderObj));

    const recurseProcess = (menuOrderItemObj: Record<string, any>, parentName: string = '') => {
      for (const [key, value] of Object.entries(menuOrderItemObj)) {
        const isRootParent = rootMenuOrderKeys.has(key);
        const currentParent = isRootParent ? key : parentName;

        if (typeof value === 'number') {
          if (currentParent) {
            flattenMenuOrderObj[this.menuOrderKeyNameGeneration(currentParent, key)] = value;
          }
        } else if (value && typeof value === 'object') {
          recurseProcess(value, key);
        }
      }
    };

    recurseProcess(menuOrderObj);

    return flattenMenuOrderObj;
  }

  private mapEnumIdsToMenuItem(item: QEMenuModuleType): QEMenuModuleType {
    const menuNameEnumMap = Object.fromEntries(Object.entries(QE_MENU_MODULE_NAME_ENUM).map(([k, v]) => [v, +k]));
    const menuNameEnumListMap = (obj: any) => Object.fromEntries(Object.entries(obj).map(([k, v]) => [v, +k]));

    return {
      ...item,
      menuParentId: menuNameEnumMap[item.parentName] ?? item.menuParentId,
      menuUniqueId: menuNameEnumListMap(QE_MENU_MODULE_NAME_ENUM_LIST[menuNameEnumMap[item.parentName]])[item.menuName] ?? item.menuUniqueId
    };
  }

  private mapOrderToMenuItem(item: QEMenuModuleType, flattenMenuOrder: Record<string, number>): QEMenuModuleType {
    const menuOrderKeyName = this.menuOrderKeyNameGeneration(item.parentName, item.menuName);
    return {
      ...item,
      frontEndMenuDisplayOrder: flattenMenuOrder[menuOrderKeyName] ?? item.frontEndMenuDisplayOrder
    };
  }

  private prepareQEMenuModuleList(qeMenuModuleList: QEMenuModuleType[]): QEMenuModuleType[] {
    const flattenMenuOrder = this.getFlattenMenuOrderList(QE_MENU_MODULE_ORDER_LIST);
    return qeMenuModuleList
      .map(this.mapEnumIdsToMenuItem)
      .map(item => this.mapOrderToMenuItem(item, flattenMenuOrder))
      .sort((a, b) => a.frontEndMenuDisplayOrder - b.frontEndMenuDisplayOrder);
  }

  setQEMenuModuleListWithEnumMapping(): QEMenuModuleType[] {
    const storedQEMenuModuleTypeList = this.storageService.get(AppConstants.qeMenuSubmenuItemListKey);
    this.qeMenuModuleList =
      storedQEMenuModuleTypeList && storedQEMenuModuleTypeList.length > 0 ? storedQEMenuModuleTypeList : this.qeMenuModuleList;
    return this.prepareQEMenuModuleList(this.qeMenuModuleList);
  }

  authenticateQEAnalyticsPassword(qeAnalyticsPasswordObj: QEAnalyticsPasswordObj): Observable<QEAnalyticsPasswordObj> {
    return this.http.post<QEAnalyticsPasswordObj>(ApiUrl.AUTHENTICATE_QE_ANALYTICS_CODE, qeAnalyticsPasswordObj);
  }

  updateQEAnalyticsPassword(qeAnalyticsPasswordObj: QEAnalyticsPasswordObj): Observable<QEAnalyticsPasswordObj> {
    return this.http.post<QEAnalyticsPasswordObj>(ApiUrl.UPDATE_QE_ANALYTICS_CODE, qeAnalyticsPasswordObj);
  }

  checkAndUpdateQEAnalyticsPassword(qeAnalyticsPasswordObj: QEAnalyticsPasswordObj): Observable<QEAnalyticsPasswordObj> {
    return this.http.post<QEAnalyticsPasswordObj>(ApiUrl.CHECK_UPDATE_QE_ANALYTICS_PASSWORD, qeAnalyticsPasswordObj);
  }

  resetQEAnalyticsPassword(qeAnalyticsPasswordObj: QEAnalyticsPasswordObj): Observable<QEAnalyticsPasswordObj> {
    return this.http.post<QEAnalyticsPasswordObj>(ApiUrl.CHECK_UPDATE_QE_ANALYTICS_PASSWORD, qeAnalyticsPasswordObj);
  }

  getQEMenuModuleList(): Observable<QEMenuModuleType[]> {
    const qeMenuModuleTypeListResponse = new Promise<QEMenuModuleType[]>((resolve, reject) => {
      if (this.qeMenuModuleList.length > 0) {
        this.storageService.set(AppConstants.qeMenuSubmenuItemListKey, this.qeMenuModuleList);
        resolve(this.qeMenuModuleList);
      } else {
        this.http.get<QEMenuModuleType[]>(ApiUrl.GET_QE_MENU_MODULE_LIST).subscribe({
          next: (res: QEMenuModuleType[]) => {
            this.qeMenuModuleList = this.prepareQEMenuModuleList(res);
            this.storageService.set(AppConstants.qeMenuSubmenuItemListKey, this.qeMenuModuleList);
            resolve(this.qeMenuModuleList);
          },
          error: e => {
            reject(e);
          }
        });
      }
    });
    return from(qeMenuModuleTypeListResponse);
  }

  captureQEMenuAnalytics(capturedQEMenuAnalytics: QEMenuModuleType): Observable<any> {
    return this.http.post<any>(ApiUrl.CAPTURE_QE_MENU_ANALYTICS, capturedQEMenuAnalytics);
  }

  getQEAnalyticsResponse(filterModel: CommonFilter): Observable<QEAnalyticsRes> {
    return this.http.post<QEAnalyticsRes>(ApiUrl.QE_ANALYTICS_BY_FILTER, filterModel);
  }
}
